---
description:
globs:
alwaysApply: false
---
# 师傅端订单流程

## 订单状态流转（师傅视角）

师傅端参与订单流程的状态主要包括：

1. **待接单 (STATUS_PENDING_ACCEPT = 1)**
   - 用户支付预约费后，订单进入师傅订单大厅
   - 师傅可以在订单大厅查看并接单
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `hall()` 方法获取可接订单

2. **待师傅上门 (STATUS_PENDING_VISIT = 2)**
   - 师傅接单后的状态
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `accept()` 方法处理接单

3. **待设定服务费用 (STATUS_PENDING_FEE = 3)**
   - 师傅到达后标记已到达
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `arrive()` 方法处理师傅到达

4. **待维修 (STATUS_PENDING_SERVICE = 4)**
   - 用户支付服务费后，师傅开始提供服务
   - 此阶段师傅可以上传施工图片和材料使用图片

5. **待维修确认 (STATUS_PENDING_CONFIRM = 5)**
   - 师傅完成服务后提交服务完成
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `verify()` 方法提交服务完成

6. **已完成 (STATUS_COMPLETED = 6)**
   - 用户确认服务完成后，订单结束
   - 系统会根据订单金额计算师傅佣金

## 师傅可执行的操作

### 查看订单大厅
- 路由：`GET /api/worker/orders/hall`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `hall()` 方法
- 获取待接单的订单列表
- 筛选条件：技能匹配、地区匹配

### 接单
- 路由：`POST /api/worker/orders/{id}/accept`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `accept()` 方法
- 验证师傅技能和服务区域匹配
- 将订单状态更新为待师傅上门

### 标记已到达
- 路由：`POST /api/worker/orders/{id}/arrive`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `arrive()` 方法
- 记录师傅到达时间
- 将订单状态更新为待设定服务费用

### 设定服务费用
- 路由：`POST /api/worker/orders/{id}/set-service-fee`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `setServiceFee()` 方法
- 设置具体服务费用
- 更新订单总金额

### 上传施工图片
- 路由：`POST /api/worker/orders/{id}/construction-images`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `uploadConstructionImages()` 方法
- 上传施工过程中的图片

### 上传材料使用图片
- 路由：`POST /api/worker/orders/{id}/material-images`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `uploadMaterialImages()` 方法
- 上传材料使用的图片

### 上传完工图片
- 路由：`POST /api/worker/orders/{id}/completion-images`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `uploadCompletionImages()` 方法
- 上传服务完成后的图片

### 提交服务完成
- 路由：`POST /api/worker/orders/{id}/verify`
- 控制器：[Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `verify()` 方法
- 提交服务完成，等待用户确认
- 核销订单，将订单状态更新为待维修确认

## 师傅收入管理

1. **佣金计算**
   - 在订单完成后，系统自动计算师傅佣金
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `calculateCommission()` 方法计算佣金
   - 佣金比例根据师傅等级可能有所不同

2. **收入记录**
   - 师傅可以查看自己的收入记录
   - 包括订单佣金、材料收入等
   - 收入记录会记录在 `worker_incomes` 表中

## 师傅接单匹配规则

1. **技能匹配**
   - 师傅只能接与自己技能匹配的服务订单
   - 技能信息存储在 `worker_skills` 表中
   - 接单时会验证师傅是否具备对应技能

2. **区域匹配**
   - 师傅只能接自己服务区域内的订单
   - 区域信息存储在 `worker_areas` 表中
   - 接单时会验证订单地址是否在师傅服务区域内

3. **接单限制**
   - 师傅可能有接单数量限制
   - 根据师傅等级不同，接单数量限制可能不同
