---
description:
globs:
alwaysApply: false
---
# 已知代码问题

## PaymentController.php 中的常量错误

在 [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `callback` 方法中，有一处错误使用了未定义的常量：

```php
$order->status = Order::STATUS_PAID; // 错误，Order模型中没有定义STATUS_PAID常量
```

此处应该使用已定义的常量 `Order::STATUS_PENDING_ACCEPT`：

```php
$order->status = Order::STATUS_PENDING_ACCEPT; // 正确，使用已定义的常量
```

订单模型中定义的正确常量为：
- `STATUS_PENDING_PAYMENT` = 0 (待付款)
- `STATUS_PENDING_ACCEPT` = 1 (待接单)
- `STATUS_PENDING_VISIT` = 2 (待师傅上门)
- 更多状态请参考 [Order.php](mdc:app/Models/Order.php) 模型
