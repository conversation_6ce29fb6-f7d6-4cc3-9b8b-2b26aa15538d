---
description:
globs:
alwaysApply: false
---
# 后台数据导出功能指南

本项目使用Laravel Excel包实现后台数据导出功能，以下是主要实现方式和规范。

## 导出功能结构

### 1. 导出类

导出类通常位于`App\Exports`命名空间下，每个导出类负责处理一种数据的导出：

```php
namespace App\Exports;

use App\Models\Order;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OrdersExport implements FromQuery, WithHeadings, WithMapping
{
    protected $startDate;
    protected $endDate;

    public function __construct($startDate = null, $endDate = null)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function query()
    {
        $query = Order::query()->with(['user', 'service', 'worker']);

        if ($this->startDate) {
            $query->where('created_at', '>=', $this->startDate.' 00:00:00');
        }

        if ($this->endDate) {
            $query->where('created_at', '<=', $this->endDate.' 23:59:59');
        }

        return $query;
    }

    public function headings(): array
    {
        return [
            'ID',
            '订单号',
            '用户名',
            '服务项目',
            '师傅',
            '预约费用',
            '服务费用',
            '材料费用',
            '总金额',
            '支付方式',
            '订单状态',
            '支付状态',
            '创建时间',
        ];
    }

    public function map($order): array
    {
        return [
            $order->id,
            $order->order_no,
            $order->user->name ?? '',
            $order->service->name ?? '',
            $order->worker->name ?? '',
            $order->booking_fee,
            $order->service_fee,
            $order->material_fee,
            $order->total_amount,
            $this->getPaymentMethodText($order->payment_method),
            $this->getStatusText($order->status),
            $this->getPaymentStatusText($order->payment_status),
            $order->created_at,
        ];
    }

    protected function getPaymentMethodText($method)
    {
        $methods = [
            'wechat' => '微信',
            'alipay' => '支付宝',
            'balance' => '余额'
        ];

        return $methods[$method] ?? '未知';
    }

    protected function getStatusText($status)
    {
        $statuses = [
            Order::STATUS_PENDING_PAYMENT => '待付款',
            Order::STATUS_PENDING_ACCEPT => '待接单',
            Order::STATUS_PENDING_VISIT => '待师傅上门',
            Order::STATUS_PENDING_FEE => '待设定服务费用',
            Order::STATUS_PENDING_SERVICE => '待维修',
            Order::STATUS_PENDING_CONFIRM => '待维修确认',
            Order::STATUS_COMPLETED => '已完成',
            Order::STATUS_CANCELLED => '已取消'
        ];

        return $statuses[$status] ?? '未知状态';
    }

    protected function getPaymentStatusText($status)
    {
        $statuses = [
            'unpaid' => '未支付',
            'partial' => '部分支付',
            'paid' => '已支付',
            'refunded' => '已退款'
        ];

        return $statuses[$status] ?? '未知';
    }
}
```

### 2. 控制器中的导出方法

在后台控制器中，通常会添加一个导出方法，示例如[OrderController.php](mdc:app/Admin/Controllers/OrderController.php)中的export方法：

```php
public function export(Request $request)
{
    $startDate = $request->input('start_date');
    $endDate = $request->input('end_date');

    return Excel::download(new OrdersExport($startDate, $endDate), '订单数据-'.date('YmdHis').'.xlsx');
}
```

### 3. 导出按钮配置

在Grid表格中添加导出按钮：

```php
$grid->tools(function ($tools) {
    $tools->append('<div class="btn-group pull-right" style="margin-right: 10px">
        <a href="javascript:void(0);" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#export-modal">
            <i class="fa fa-download"></i> 导出数据
        </a>
    </div>
    <div class="modal fade" id="export-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">导出数据</h4>
                </div>
                <form action="'.admin_url('orders/export').'" method="post" pjax-container>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>开始日期</label>
                            <input type="date" class="form-control" name="start_date">
                        </div>
                        <div class="form-group">
                            <label>结束日期</label>
                            <input type="date" class="form-control" name="end_date">
                        </div>
                        '.csrf_field().'
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">导出</button>
                    </div>
                </form>
            </div>
        </div>
    </div>');
});
```

## 导出类接口

Laravel Excel支持多种导出接口实现方式：

### 1. FromQuery

从Eloquent查询中导出数据：

```php
public function query()
{
    return Model::query()->where('active', 1);
}
```

### 2. FromCollection

从集合中导出数据：

```php
public function collection()
{
    return Model::all();
}
```

### 3. FromArray

从数组中导出数据：

```php
public function array(): array
{
    return [
        [1, 2, 3],
        [4, 5, 6]
    ];
}
```

### 4. FromView

从Blade视图中导出数据：

```php
public function view(): View
{
    return view('exports.invoices', [
        'invoices' => Invoice::all()
    ]);
}
```

## 导出功能扩展接口

### 1. WithHeadings

添加表头：

```php
public function headings(): array
{
    return [
        'ID',
        '名称',
        '描述'
    ];
}
```

### 2. WithMapping

转换数据格式：

```php
public function map($row): array
{
    return [
        $row->id,
        $row->name,
        $row->description
    ];
}
```

### 3. WithStyles

自定义样式：

```php
public function styles(Worksheet $sheet)
{
    return [
        // 第一行样式
        1 => ['font' => ['bold' => true]],

        // B列样式
        'B' => ['font' => ['italic' => true]],

        // B2单元格样式
        'B2' => ['font' => ['bold' => true]],
    ];
}
```

### 4. WithColumnFormatting

设置列格式：

```php
public function columnFormats(): array
{
    return [
        'A' => NumberFormat::FORMAT_NUMBER,
        'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
    ];
}
```

## 最佳实践

1. 使用**分批查询**处理大量数据导出，避免内存溢出
2. 为导出类添加适当的筛选参数，支持条件导出
3. 在视图中提供直观的导出条件选择界面
4. 使用队列处理大型导出任务
5. 为导出文件添加时间戳或唯一标识符，避免文件覆盖
6. 在适当的地方添加权限检查，控制导出功能的访问
