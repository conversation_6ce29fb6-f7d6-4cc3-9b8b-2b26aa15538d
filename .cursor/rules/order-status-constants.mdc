---
description:
globs:
alwaysApply: false
---
# 订单状态常量

订单状态常量定义在 [Order.php](mdc:app/Models/Order.php) 模型中：

```php
// 订单状态常量
const STATUS_PENDING_PAYMENT = 0; // 待付款
const STATUS_PENDING_ACCEPT = 1;  // 待接单
const STATUS_PENDING_VISIT = 2;   // 待师傅上门
const STATUS_PENDING_FEE = 3;     // 待设定服务费用
const STATUS_PENDING_SERVICE = 4; // 待维修
const STATUS_PENDING_CONFIRM = 5; // 待维修确认
const STATUS_COMPLETED = 6;       // 已完成
const STATUS_CANCELLED = 7;       // 已取消

// 支付方式常量
const PAYMENT_METHOD_WECHAT = 'wechat';   // 微信支付
const PAYMENT_METHOD_ALIPAY = 'alipay';   // 支付宝支付
const PAYMENT_METHOD_BALANCE = 'balance'; // 余额支付
```

## 订单状态流转图

```
创建订单
   |
   ↓
待付款 (STATUS_PENDING_PAYMENT = 0)
   |
   | 支付预约费
   ↓
待接单 (STATUS_PENDING_ACCEPT = 1)
   |
   | 师傅接单
   ↓
待师傅上门 (STATUS_PENDING_VISIT = 2)
   |
   | 师傅到达
   ↓
待设定服务费用 (STATUS_PENDING_FEE = 3)
   |
   | 设定费用并支付
   ↓
待维修 (STATUS_PENDING_SERVICE = 4)
   |
   | 服务完成
   ↓
待维修确认 (STATUS_PENDING_CONFIRM = 5)
   |
   | 用户确认
   ↓
已完成 (STATUS_COMPLETED = 6)
```

## 状态文本转换

在控制器中常见的状态文本转换方法：

```php
protected function getStatusText($status)
{
    switch ($status) {
        case Order::STATUS_PENDING_PAYMENT:
            return '待付款';
        case Order::STATUS_PENDING_ACCEPT:
            return '待接单';
        case Order::STATUS_PENDING_VISIT:
            return '待师傅上门';
        case Order::STATUS_PENDING_FEE:
            return '待设定服务费用';
        case Order::STATUS_PENDING_SERVICE:
            return '待维修';
        case Order::STATUS_PENDING_CONFIRM:
            return '待维修确认';
        case Order::STATUS_COMPLETED:
            return '已完成';
        case Order::STATUS_CANCELLED:
            return '已取消';
        default:
            return '未知状态';
    }
}
```

## 订单状态日志记录

每次状态变更都会记录在 `order_status_logs` 表中，记录字段包括：

- `order_id`: 订单ID
- `previous_status`: 前一状态
- `current_status`: 当前状态
- `operator_id`: 操作人ID
- `operator_type`: 操作人类型（user/worker/admin）
- `remark`: 备注
- `created_at`: 创建时间
