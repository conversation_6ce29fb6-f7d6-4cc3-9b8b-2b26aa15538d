---
description:
globs:
alwaysApply: false
---
# 用户端订单流程

## 订单状态流转
订单在系统中的状态流转如下：

1. **待付款 (STATUS_PENDING_PAYMENT = 0)**
   - 用户创建订单后的初始状态
   - [OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `store()` 方法创建订单

2. **待接单 (STATUS_PENDING_ACCEPT = 1)**
   - 用户支付预约费后的状态
   - [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `callback()` 方法处理支付回调，将订单状态更新为待接单

3. **待师傅上门 (STATUS_PENDING_VISIT = 2)**
   - 师傅接单后的状态
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `accept()` 方法处理师傅接单

4. **待设定服务费用 (STATUS_PENDING_FEE = 3)**
   - 师傅到达后，需要设定具体服务费用
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `arrive()` 方法记录师傅到达

5. **待维修 (STATUS_PENDING_SERVICE = 4)**
   - 用户支付服务费后的状态
   - [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 处理服务费支付回调

6. **待维修确认 (STATUS_PENDING_CONFIRM = 5)**
   - 师傅完成服务，等待用户确认
   - [Worker/OrderController.php](mdc:app/Http/Controllers/Api/Worker/OrderController.php) 的 `verify()` 方法提交服务完成

7. **已完成 (STATUS_COMPLETED = 6)**
   - 用户确认服务完成
   - [OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `complete()` 方法确认订单完成

8. **已取消 (STATUS_CANCELLED = 7)**
   - 订单被取消
   - [OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `cancel()` 方法取消订单

## 用户可执行的操作

### 创建订单
- 路由：`POST /api/user/orders`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `store()` 方法
- 必要参数：服务ID、地址ID、预约日期、预约时间、支付方式
- 生成订单号、核销码，初始状态为待付款

### 支付预约费
- 路由：`POST /api/user/orders/{id}/pay-booking`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `payBookingFee()` 方法
- 创建支付记录，生成支付链接
- 支付完成后状态更新为待接单

### 支付服务费
- 路由：`POST /api/user/orders/{id}/pay-service`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `payServiceFee()` 方法
- 创建服务费支付记录，生成支付链接
- 支付完成后状态更新为待维修

### 确认服务完成
- 路由：`POST /api/user/orders/{id}/complete`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `complete()` 方法
- 将订单状态更新为已完成

### 取消订单
- 路由：`POST /api/user/orders/{id}/cancel`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `cancel()` 方法
- 仅待接单状态可以取消
- 取消后可能触发退款流程

### 评价订单
- 路由：`POST /api/user/orders/{id}/review`
- 控制器：[OrderController.php](mdc:app/Http/Controllers/Api/User/OrderController.php) 的 `review()` 方法
- 仅已完成订单可以评价
- 评分范围：1-5分

## 订单支付流程

1. **创建支付记录**
   - [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `create()` 方法
   - 根据支付类型和支付方式创建支付记录

2. **处理支付回调**
   - [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `callback()` 方法
   - 验证支付结果，更新订单状态

3. **查询支付状态**
   - [PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `status()` 方法
   - 前端轮询支付状态

## 订单状态日志记录
- 每次订单状态变更都会记录在 `order_status_logs` 表中
- 记录内容包括：前一状态、当前状态、操作人、操作类型、备注
- 记录方法：`$order->statusLogs()->create([...])`
