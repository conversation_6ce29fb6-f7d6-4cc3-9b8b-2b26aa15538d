---
description:
globs:
alwaysApply: false
---
# Dcat Admin 使用指南

本项目使用Dcat Admin作为后台管理框架，下面是框架的关键组件和使用方式。

## 核心组件

### 1. 数据表格 (Grid)

Grid用于展示数据列表，支持排序、筛选、分页等功能：

```php
protected function grid()
{
    return Grid::make(Model::class, function (Grid $grid) {
        // 定义列
        $grid->column('id', 'ID');
        $grid->column('name', '名称');

        // 定义筛选器
        $grid->filter(function (Grid\Filter $filter) {
            $filter->equal('id');
            $filter->like('name', '名称');
        });

        // 自定义行操作
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->append('<a class="btn btn-sm btn-primary">自定义按钮</a>');
        });
    });
}
```

### 2. 详情页 (Show)

Show用于展示单条数据的详细信息：

```php
protected function detail($id)
{
    return Show::make($id, Model::class, function (Show $show) {
        // 显示字段
        $show->field('id', 'ID');
        $show->field('name', '名称');

        // 关联关系
        $show->relation('关联数据', function ($model) {
            // 返回关联数据的表格或其他展示组件
        });
    });
}
```

### 3. 表单 (Form)

Form用于创建和编辑数据：

```php
protected function form()
{
    return Form::make(Model::class, function (Form $form) {
        // 基本表单控件
        $form->display('id', 'ID');
        $form->text('name', '名称')->required();
        $form->textarea('description', '描述');
        $form->select('status', '状态')->options([
            0 => '禁用',
            1 => '启用'
        ]);

        // 保存前回调
        $form->saving(function (Form $form) {
            // 处理表单数据
        });
    });
}
```

### 4. 操作 (Actions)

自定义后台操作按钮，通常用于批量操作或行操作：

```php
use Dcat\Admin\Grid\Tools\BatchActions;

$grid->batchActions(function (BatchActions $actions) {
    $actions->add(new YourBatchAction());
});
```

### 5. 菜单配置

菜单配置位于数据库中，通过管理页面可配置：

- 路由配置
- 图标设置
- 权限绑定
- 排序管理

## 数据表格功能

### 1. 列操作

```php
// 基本列定义
$grid->column('field_name', '显示名称');

// 值映射
$grid->column('status')->using([0 => '禁用', 1 => '启用']);

// 样式标签
$grid->column('status')->label([0 => 'danger', 1 => 'success']);

// 图片处理
$grid->column('image')->image();

// 链接处理
$grid->column('url')->link();
```

### 2. 筛选器

```php
$grid->filter(function (Grid\Filter $filter) {
    // 去掉默认的id过滤器
    $filter->disableIdFilter();

    // 添加字段过滤器
    $filter->equal('status', '状态');
    $filter->like('name', '名称');
    $filter->between('created_at', '创建时间')->datetime();
    $filter->where('name', function ($query) {
        $query->where('name', 'like', "%{$this->input}%");
    }, '名称');
});
```

### 3. 工具栏

```php
$grid->tools(function ($tools) {
    // 添加自定义按钮
    $tools->append('<a class="btn btn-primary">导出</a>');
});
```

## 表单组件

### 1. 常用表单控件

```php
// 文本输入
$form->text('field', 'Label');

// 数字输入
$form->number('sort', '排序');

// 下拉选择
$form->select('category_id', '分类')->options(Category::pluck('name', 'id'));

// 单选
$form->radio('status', '状态')->options([0 => '禁用', 1 => '启用']);

// 多选
$form->checkbox('permissions', '权限')->options(Permission::pluck('name', 'id'));

// 日期时间
$form->datetime('published_at', '发布时间');

// 图片上传
$form->image('avatar', '头像')->disk('public')->dir('images/avatars');

// 文件上传
$form->file('attachment', '附件')->disk('public')->dir('files');

// 富文本编辑器
$form->editor('content', '内容');
```

### 2. 表单回调

```php
// 保存前
$form->saving(function (Form $form) {
    // 处理请求数据
});

// 保存后
$form->saved(function (Form $form) {
    // 处理后续逻辑
});

// 删除前
$form->deleting(function (Form $form) {
    // 删除前处理
});
```

## 权限管理

Dcat Admin提供完整的RBAC权限管理：

- 角色管理
- 权限配置
- 菜单权限绑定
- 操作权限控制

```php
// 检查当前用户是否有权限
if (Admin::user()->can('permission-name')) {
    // 有权限的操作
}

// 为控制器方法添加权限
$grid->actions(function (Grid\Displayers\Actions $actions) {
    if (!Admin::user()->can('edit-post')) {
        $actions->disableEdit();
    }

    if (!Admin::user()->can('delete-post')) {
        $actions->disableDelete();
    }
});
```
