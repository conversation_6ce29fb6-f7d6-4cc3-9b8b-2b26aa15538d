---
description:
globs:
alwaysApply: false
---
# 支付流程说明

## 支付类型
系统支持以下支付类型：

1. **预约费支付 (booking)**
   - 创建订单后需支付的预约费用
   - 成功支付后订单状态变为待接单

2. **服务费支付 (service)**
   - 师傅设定服务费用后，用户需支付的服务费
   - 成功支付后订单状态变为待维修

3. **材料费支付 (material)**
   - 用户购买材料时的支付
   - 与维修服务订单可能关联

4. **充值支付 (recharge)**
   - 用户充值到钱包的支付

## 支付方式
系统支持以下支付方式：

1. **微信支付 (wechat)**
   - `Order::PAYMENT_METHOD_WECHAT`
   - 通过微信支付接口处理

2. **支付宝支付 (alipay)**
   - `Order::PAYMENT_METHOD_ALIPAY`
   - 通过支付宝接口处理

3. **余额支付 (balance)**
   - `Order::PAYMENT_METHOD_BALANCE`
   - 从用户钱包余额中扣除

## 支付处理流程

### 创建支付记录
- 控制器：[PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `create()` 方法
- 根据支付类型、支付方式创建支付记录
- 生成支付链接返回给前端

```php
// 创建支付记录
$payment = Payment::create([
    'payment_no' => $paymentNo,
    'user_id' => $user->id,
    'order_id' => $orderId,
    'material_order_id' => $materialOrderId,
    'recharge_id' => $rechargeId,
    'amount' => $amount,
    'payment_type' => $paymentType,
    'payment_method' => $paymentMethod,
    'status' => Payment::STATUS_PENDING,
]);
```

### 支付回调处理
- 控制器：[PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `callback()` 方法
- 验证支付结果，更新支付记录状态
- 根据支付类型执行相应的业务逻辑：
  1. 预约费支付：更新订单状态为待接单
  2. 服务费支付：更新订单状态为待维修
  3. 材料费支付：更新材料订单状态
  4. 充值支付：增加用户钱包余额

```php
// 支付成功处理
if ($payment->payment_type == Payment::PAYMENT_TYPE_BOOKING) {
    // 处理订单预约费支付
    $order = Order::findOrFail($payment->order_id);
    $order->status = Order::STATUS_PENDING_ACCEPT;
    $order->booking_paid_at = now();
    $order->save();

    // 记录订单状态变更
    $order->statusLogs()->create([
        'previous_status' => Order::STATUS_PENDING_PAYMENT,
        'current_status' => Order::STATUS_PENDING_ACCEPT,
        'operator_id' => $order->user_id,
        'operator_type' => 'system',
        'remark' => '用户支付预约费，订单状态更新为待接单',
    ]);
} elseif ($payment->payment_type == Payment::PAYMENT_TYPE_SERVICE) {
    // 处理服务费支付
    // ...
}
```

### 支付状态查询
- 控制器：[PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 的 `status()` 方法
- 前端轮询支付状态
- 返回支付结果给前端

## 支付记录状态

支付记录状态在 [Payment.php](mdc:app/Models/Payment.php) 中定义：

```php
// 支付状态
const STATUS_PENDING = 0;   // 待支付
const STATUS_SUCCESS = 1;   // 支付成功
const STATUS_FAILED = 2;    // 支付失败
const STATUS_REFUNDED = 3;  // 已退款
```

## 退款处理流程

- 控制器：[RefundController.php](mdc:app/Http/Controllers/Api/User/RefundController.php)
- 在订单取消时可能触发退款流程
- 根据支付方式调用相应的退款接口
- 更新支付记录状态为已退款
