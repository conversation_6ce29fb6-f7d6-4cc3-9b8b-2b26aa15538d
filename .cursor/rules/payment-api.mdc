---
description:
globs:
alwaysApply: false
---
# 支付API接口规范

## API接口概览

支付模块提供完整的支付功能API，包括预支付信息获取、支付订单创建、回调处理、状态查询等功能。

## 接口认证

所有支付API接口都需要用户登录认证，使用Bearer Token方式：
```
Authorization: Bearer {access_token}
```

## 统一响应格式

所有API接口统一返回JSON格式：
```json
{
  "code": 200,           // 状态码：200成功，其他为失败
  "message": "success",  // 响应消息
  "data": { ... }        // 响应数据
}
```

## 核心API接口

### 1. 预支付信息获取

#### 接口信息
- **URL**: `POST /api/user/payments/prepay`
- **控制器**: [PaymentController@prepay](mdc:app/Http/Controllers/Api/User/PaymentController.php)
- **功能**: 获取支付前的相关信息，包括支付金额、可用支付方式、优惠券等

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| order_id | integer | 否 | 订单ID（预约费、服务费、尾款支付时必填） | 123 |
| material_order_id | integer | 否 | 材料订单ID（材料费支付时必填） | 456 |
| payment_type | string | 是 | 支付类型 | appointment |
| amount | decimal | 否 | 充值金额（充值时必填） | 100.00 |

#### 支付类型说明
- `appointment`: 预约费支付
- `service`: 服务费支付
- `material`: 材料费支付
- `recharge`: 充值支付
- `final`: 尾款支付

#### 响应示例
```json
{
  "code": 200,
  "message": "获取预支付信息成功",
  "data": {
    "order_info": {
      "id": 123,
      "order_no": "ORD20241227001",
      "service_name": "空调维修",
      "address": "北京市朝阳区xxx",
      "contact_name": "张三",
      "contact_phone": "13800138000"
    },
    "pay_amount": 50.00,
    "payment_methods": [
      {
        "id": "wechat",
        "name": "微信支付",
        "icon": "/images/payment/wechat.png"
      },
      {
        "id": "alipay",
        "name": "支付宝",
        "icon": "/images/payment/alipay.png"
      },
      {
        "id": "balance",
        "name": "余额支付",
        "icon": "/images/payment/balance.png",
        "balance": 200.00,
        "available": true
      }
    ],
    "coupons": [
      {
        "id": 1,
        "name": "新用户优惠券",
        "discount_amount": 10.00,
        "min_amount": 30.00,
        "available": true
      }
    ]
  }
}
```

### 2. 创建支付订单

#### 接口信息
- **URL**: `POST /api/user/payments/create`
- **控制器**: [PaymentController@create](mdc:app/Http/Controllers/Api/User/PaymentController.php)
- **功能**: 创建支付订单，返回支付链接或直接完成支付

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| order_id | integer | 否 | 订单ID（预约费、服务费、尾款支付时必填） | 123 |
| material_order_id | integer | 否 | 材料订单ID（材料费支付时必填） | 456 |
| payment_type | string | 是 | 支付类型 | appointment |
| payment_method | string | 是 | 支付方式 | wechat |
| coupon_id | integer | 否 | 优惠券ID | 1 |
| amount | decimal | 否 | 充值金额（充值时必填） | 100.00 |

#### 支付方式说明
- `wechat`: 微信支付
- `alipay`: 支付宝支付
- `balance`: 余额支付

#### 第三方支付响应示例
```json
{
  "code": 200,
  "message": "支付订单创建成功",
  "data": {
    "payment_id": 789,
    "payment_no": "PAY20241227123456",
    "amount": 40.00,
    "original_amount": 50.00,
    "discount_amount": 10.00,
    "payment_method": "wechat",
    "qr_code": "weixin://wxpay/bizpayurl?pr=abc123",
    "code_url": "weixin://wxpay/bizpayurl?pr=abc123",
    "prepay_id": "wx271234567890123456789012345678901234567890"
  }
}
```

#### 余额支付响应示例
```json
{
  "code": 200,
  "message": "余额支付成功",
  "data": {
    "payment_id": 789,
    "payment_no": "PAY20241227123456",
    "amount": 40.00,
    "original_amount": 50.00,
    "discount_amount": 10.00,
    "payment_method": "balance",
    "status": "paid",
    "paid_at": "2024-12-27 12:34:56"
  }
}
```

### 3. 支付回调处理

#### 接口信息
- **URL**: `POST /api/user/payments/callback?payment_method=wechat`
- **控制器**: [PaymentController@callback](mdc:app/Http/Controllers/Api/User/PaymentController.php)
- **功能**: 处理第三方支付平台的回调通知

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| payment_method | string | 是 | 支付方式（URL参数） | wechat |

#### 回调数据
回调数据由第三方支付平台发送，格式因支付方式而异。

#### 微信支付回调响应（XML格式）
```xml
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
```

#### 其他支付方式回调响应（JSON格式）
```json
{
  "code": 200,
  "message": "回调处理成功",
  "data": null
}
```

### 4. 查询支付状态

#### 接口信息
- **URL**: `GET /api/user/payments/status`
- **控制器**: [PaymentController@status](mdc:app/Http/Controllers/Api/User/PaymentController.php)
- **功能**: 查询支付订单的当前状态

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| payment_no | string | 是 | 支付订单号 | PAY20241227123456 |

#### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "payment_id": 789,
    "payment_no": "PAY20241227123456",
    "status": "paid",
    "amount": 40.00,
    "payment_type": "appointment",
    "payment_method": "wechat",
    "paid_at": "2024-12-27 12:34:56"
  }
}
```

### 5. 获取支付记录

#### 接口信息
- **URL**: `GET /api/user/payments/records`
- **控制器**: [PaymentController@records](mdc:app/Http/Controllers/Api/User/PaymentController.php)
- **功能**: 获取用户的支付记录列表

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| page | integer | 否 | 页码（默认1） | 1 |
| limit | integer | 否 | 每页数量（默认10） | 10 |
| payment_type | string | 否 | 支付类型筛选 | appointment |
| status | string | 否 | 支付状态筛选 | paid |
| start_time | string | 否 | 开始时间 | 2024-01-01 |
| end_time | string | 否 | 结束时间 | 2024-12-31 |

#### 响应示例
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "current_page": 1,
    "per_page": 10,
    "list": [
      {
        "id": 789,
        "payment_no": "PAY20241227123456",
        "order_id": 123,
        "material_order_id": null,
        "payment_type": "appointment",
        "payment_method": "wechat",
        "amount": 40.00,
        "original_amount": 50.00,
        "discount_amount": 10.00,
        "status": "paid",
        "created_at": "2024-12-27 12:30:00",
        "paid_at": "2024-12-27 12:34:56"
      }
    ]
  }
}
```

## 错误码说明

### 通用错误码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（需要登录）
- `403`: 禁止访问
- `404`: 资源不存在
- `422`: 验证失败
- `500`: 服务器内部错误

### 支付相关错误码
- `40001`: 订单不存在
- `40002`: 订单状态不允许支付
- `40003`: 支付金额错误
- `40004`: 余额不足
- `40005`: 优惠券不可用
- `40006`: 支付方式不支持
- `40007`: 支付订单已存在
- `40008`: 支付回调验证失败

## 支付状态说明

### 支付记录状态
- `pending`: 待支付
- `paid`: 已支付
- `failed`: 支付失败
- `refunded`: 已退款

### 订单支付状态
- 预约费支付成功：订单状态变为"待接单"
- 服务费支付成功：订单状态变为"待维修"
- 尾款支付成功：订单状态保持不变，记录支付信息
- 材料费支付成功：材料订单状态变为"待使用"
- 充值支付成功：用户钱包余额增加

## 安全注意事项

### 支付金额验证
- 前端传入的金额仅作参考，实际金额以后端计算为准
- 支付回调中必须验证金额的一致性
- 使用decimal类型存储金额，避免浮点数精度问题

### 回调安全
- 验证回调来源的合法性
- 验证签名防止数据篡改
- 防止重复处理回调（幂等性）
- 记录详细的回调日志

### 数据安全
- 敏感信息不在日志中记录
- 支付密钥等配置信息加密存储
- API接口添加频率限制

## 测试指南

### 开发环境测试
1. 使用支付平台提供的沙箱环境
2. 配置测试商户号和密钥
3. 使用测试金额（如0.01元）进行测试

### 测试场景
1. 正常支付流程测试
2. 支付失败场景测试
3. 网络异常重试测试
4. 并发支付测试
5. 回调重复处理测试

### 测试数据
- 测试订单：使用特定前缀标识
- 测试金额：使用小额金额
- 测试用户：创建专门的测试账号

## 监控和日志

### 关键指标监控
- 支付成功率
- 支付响应时间
- 回调处理成功率
- 异常支付订单数量

### 日志记录
- 支付订单创建日志
- 支付回调处理日志
- 支付异常错误日志
- 支付状态变更日志

### 告警机制
- 支付成功率低于阈值告警
- 支付异常数量超过阈值告警
- 回调处理失败告警
- 支付金额异常告警
