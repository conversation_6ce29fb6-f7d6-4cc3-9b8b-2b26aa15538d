---
description:
globs:
alwaysApply: true
---
# 项目概览：本地服务平台

本项目是一个提供本地维修、家政等服务的平台，包含用户端和师傅端两个应用，以及管理后台。

## 系统架构

### 后端技术栈
- PHP 8.1+
- Laravel 10.x
- Dcat Admin 2.x
- MySQL 8.0+
- RESTful API

### 前端技术栈
- Vue 3
- Uniapp (小程序端)

## 主要功能模块

1. **用户认证模块** - 用户注册、登录、个人信息管理
2. **服务管理模块** - 服务项目管理、分类和展示
3. **订单管理模块** - 订单创建、派单、状态流转
4. **支付模块** - 支付管理、退款管理、充值功能
5. **师傅管理模块** - 师傅注册认证、接单管理
6. **材料管理模块** - 材料商城、购物车、材料订单
7. **内容管理模块** - 招聘信息、课程管理
8. **营销管理模块** - 营销活动、优惠券、会员套餐
9. **统计分析模块** - 各类数据统计和分析
10. **系统管理模块** - 权限管理、参数配置

## 应用类型
1. **用户端** - 小程序/H5应用
2. **师傅端** - 小程序/H5应用
3. **管理后台** - Dcat Admin

## 项目特点

- 遵循 Laravel 和 PHP 最佳实践
- 使用 RESTful API 设计规范
- 实现完整的订单流程管理
- 集成支付功能
- 多角色用户系统（用户、师傅、管理员）
