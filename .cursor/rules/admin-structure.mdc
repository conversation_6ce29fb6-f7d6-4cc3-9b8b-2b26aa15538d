---
description:
globs:
alwaysApply: false
---
# 后台管理系统结构

后台管理系统基于Dcat Admin构建，提供各种业务管理功能。

## 目录结构
- `app/Admin/Controllers/` - 所有后台控制器
- `app/Admin/Actions/` - 自定义操作类
- `app/Admin/Metrics/` - 数据统计卡片
- `app/Admin/routes.php` - 后台路由定义
- `app/Admin/bootstrap.php` - 后台初始化配置

## 主要功能模块

### 1. 用户管理
- [UserController.php](mdc:app/Admin/Controllers/UserController.php) - 用户管理
- [UserConsumptionController.php](mdc:app/Admin/Controllers/UserConsumptionController.php) - 用户消费记录

### 2. 工人管理
- [WorkerController.php](mdc:app/Admin/Controllers/WorkerController.php) - 师傅管理
- [WorkerLevelController.php](mdc:app/Admin/Controllers/WorkerLevelController.php) - 师傅等级
- [WorkerSkillController.php](mdc:app/Admin/Controllers/WorkerSkillController.php) - 师傅技能
- [WorkerQuitRequestController.php](mdc:app/Admin/Controllers/WorkerQuitRequestController.php) - 师傅退出申请

### 3. 订单管理
- [OrderController.php](mdc:app/Admin/Controllers/OrderController.php) - 订单管理
- [OrderReviewController.php](mdc:app/Admin/Controllers/OrderReviewController.php) - 订单评价
- [PaymentController.php](mdc:app/Admin/Controllers/PaymentController.php) - 支付管理

### 4. 服务管理
- [ServiceController.php](mdc:app/Admin/Controllers/ServiceController.php) - 服务管理
- [ServiceCategoryController.php](mdc:app/Admin/Controllers/ServiceCategoryController.php) - 服务分类
- [SkillController.php](mdc:app/Admin/Controllers/SkillController.php) - 技能管理
- [CancellationFeeController.php](mdc:app/Admin/Controllers/CancellationFeeController.php) - 取消费用

### 5. 材料管理
- [MaterialController.php](mdc:app/Admin/Controllers/MaterialController.php) - 材料管理
- [MaterialCategoryController.php](mdc:app/Admin/Controllers/MaterialCategoryController.php) - 材料分类
- [MaterialOrderController.php](mdc:app/Admin/Controllers/MaterialOrderController.php) - 材料订单

### 6. 内容管理
- [ArticleController.php](mdc:app/Admin/Controllers/ArticleController.php) - 文章管理
- [ArticleCategoryController.php](mdc:app/Admin/Controllers/ArticleCategoryController.php) - 文章分类
- [BannerController.php](mdc:app/Admin/Controllers/BannerController.php) - 轮播图管理
- [PageController.php](mdc:app/Admin/Controllers/PageController.php) - 页面管理
- [JobController.php](mdc:app/Admin/Controllers/JobController.php) - 招聘管理
- [JobApplicationController.php](mdc:app/Admin/Controllers/JobApplicationController.php) - 应聘申请
- [CourseController.php](mdc:app/Admin/Controllers/CourseController.php) - 课程管理
- [CourseCategoryController.php](mdc:app/Admin/Controllers/CourseCategoryController.php) - 课程分类
- [MainCourseController.php](mdc:app/Admin/Controllers/MainCourseController.php) - 主课程
- [CourseSectionController.php](mdc:app/Admin/Controllers/CourseSectionController.php) - 课程章节
- [ExamSubjectController.php](mdc:app/Admin/Controllers/ExamSubjectController.php) - 考试科目

### 7. 营销管理
- [RechargeActivityController.php](mdc:app/Admin/Controllers/RechargeActivityController.php) - 充值活动
- [CouponController.php](mdc:app/Admin/Controllers/CouponController.php) - 优惠券管理
- [AnnualPackageController.php](mdc:app/Admin/Controllers/AnnualPackageController.php) - 年度套餐

### 8. 系统管理
- [AreaController.php](mdc:app/Admin/Controllers/AreaController.php) - 区域管理
- [SystemParamController.php](mdc:app/Admin/Controllers/SystemParamController.php) - 系统参数
- [AppointmentTimeSlotController.php](mdc:app/Admin/Controllers/AppointmentTimeSlotController.php) - 预约时间段
- [StatisticsController.php](mdc:app/Admin/Controllers/StatisticsController.php) - 统计分析

## 后台路由

后台路由定义在[routes.php](mdc:app/Admin/routes.php)文件中，以资源路由为主，如：

```php
$router->resource('orders', 'OrderController');
```

特殊操作通过自定义路由实现，如：

```php
$router->get('orders/{id}/assign', 'OrderController@assign')->name('orders.assign');
$router->put('orders/{id}/do-assign', 'OrderController@doAssign')->name('orders.do-assign');
```

## 控制器结构

后台控制器通常包含以下主要方法：
- `grid()` - 数据表格配置
- `detail()` - 数据详情页配置
- `form()` - 表单配置
- 自定义操作方法，如`assign()`、`export()`等
