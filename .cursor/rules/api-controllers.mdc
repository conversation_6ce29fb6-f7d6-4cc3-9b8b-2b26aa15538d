---
description:
globs:
alwaysApply: false
---
# API控制器结构

## 目录结构
- `app/Http/Controllers/Api/User/` - 用户端API
- `app/Http/Controllers/Api/Worker/` - 师傅端API
- `app/Http/Controllers/Api/Client/` - 公共/内容API
- `app/Http/Controllers/Api/Marketing/` - 营销相关API

## 支付控制器
[PaymentController.php](mdc:app/Http/Controllers/Api/User/PaymentController.php) 提供以下功能：
- 创建支付订单: `create()`
- 支付回调处理: `callback()`
- 查询支付状态: `status()`
- 获取支付记录: `records()`

支付类型包括：appointment(预约), service(服务费), material(材料), recharge(充值)
支付方式包括：wechat(微信), alipay(支付宝)

## 订单状态
订单状态常量定义在 [Order.php](mdc:app/Models/Order.php) 模型中：
- STATUS_PENDING_PAYMENT = 0 (待付款)
- STATUS_PAID = 1 (已付款，待接单)

## API响应格式
所有API统一返回JSON格式：
```
{
  "code": 0, // 0表示成功，非0表示失败
  "message": "success",
  "data": { ... }
}
```
