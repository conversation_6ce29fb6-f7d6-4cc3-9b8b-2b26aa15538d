---
description:
globs:
alwaysApply: false
---
# 数据模型结构和关系

## 核心业务模型

### 用户相关
- [User.php](mdc:app/Models/User.php) - 用户模型
- [Worker.php](mdc:app/Models/Worker.php) - 师傅模型

### 订单相关
- [Order.php](mdc:app/Models/Order.php) - 服务订单模型，含状态常量
- [OrderStatusLog.php](mdc:app/Models/OrderStatusLog.php) - 订单状态日志

### 支付相关
- [Payment.php](mdc:app/Models/Payment.php) - 支付记录
- [Refund.php](mdc:app/Models/Refund.php) - 退款记录

### 营销相关
- [Coupon.php](mdc:app/Models/Coupon.php) - 优惠券
- [UserCoupon.php](mdc:app/Models/UserCoupon.php) - 用户优惠券

### 材料相关
- [Material.php](mdc:app/Models/Material.php) - 材料
- [MaterialOrder.php](mdc:app/Models/MaterialOrder.php) - 材料订单

## 订单状态常量
订单模型中定义了以下状态常量：
- STATUS_PENDING_PAYMENT = 0 (待付款)
- STATUS_PENDING_ACCEPT = 1 (待接单)
- STATUS_PENDING_VISIT = 2 (待师傅上门)
- STATUS_PENDING_FEE = 3 (待设定服务费用)
- STATUS_PENDING_SERVICE = 4 (待维修)
- STATUS_PENDING_CONFIRM = 5 (待维修确认)
- STATUS_COMPLETED = 6 (已完成)
- STATUS_CANCELLED = 7 (已取消)
