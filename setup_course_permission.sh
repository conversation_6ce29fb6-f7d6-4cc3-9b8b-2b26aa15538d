#!/bin/bash

# 课程权限管理功能安装脚本
# 执行此脚本来应用所有必要的更改

echo "=========================================="
echo "正在设置课程权限管理功能..."
echo "=========================================="

# 1. 检查必要的文件是否存在
echo "检查必要文件..."

required_files=(
    "app/Admin/Controllers/CoursePermissionController.php"
    "app/Admin/Actions/BatchApproveCoursePermission.php"
    "app/Admin/Actions/BatchRejectCoursePermission.php"
    "sql/add_course_permission_menu.sql"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 缺少必要文件 $file"
        exit 1
    else
        echo "✓ $file 存在"
    fi
done

# 2. 执行数据库脚本
echo ""
echo "执行数据库脚本..."

# 检查是否配置了数据库连接
if [ ! -f ".env" ]; then
    echo "错误: 未找到 .env 文件，请确保已配置环境变量"
    exit 1
fi

# 执行SQL脚本
echo "正在执行菜单和权限配置脚本..."
php artisan db:seed --class=DatabaseSeeder 2>/dev/null || echo "注意: DatabaseSeeder 可能不存在，这是正常的"

# 直接使用MySQL命令执行SQL脚本
if command -v mysql &> /dev/null; then
    echo "使用 MySQL 命令执行SQL脚本..."

    # 从.env文件读取数据库配置
    DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
    DB_DATABASE=$(grep DB_DATABASE .env | cut -d '=' -f2)
    DB_USERNAME=$(grep DB_USERNAME .env | cut -d '=' -f2)
    DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d '=' -f2)

    mysql -h"${DB_HOST}" -u"${DB_USERNAME}" -p"${DB_PASSWORD}" "${DB_DATABASE}" < sql/add_course_permission_menu.sql

    if [ $? -eq 0 ]; then
        echo "✓ 数据库脚本执行成功"
    else
        echo "⚠ 数据库脚本执行失败，请手动执行 sql/add_course_permission_menu.sql"
    fi
else
    echo "⚠ 未找到 MySQL 命令，请手动执行 sql/add_course_permission_menu.sql"
fi

# 3. 清除缓存
echo ""
echo "清除应用缓存..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

echo "✓ 缓存已清除"

# 4. 检查路由
echo ""
echo "检查路由配置..."
php artisan route:list --name=course-permissions 2>/dev/null | head -10

# 5. 验证文件权限
echo ""
echo "检查文件权限..."
chmod -R 755 app/Admin/Controllers/
chmod -R 755 app/Admin/Actions/

echo "✓ 文件权限已设置"

echo ""
echo "=========================================="
echo "课程权限管理功能安装完成！"
echo "=========================================="
echo ""
echo "接下来的步骤："
echo "1. 访问管理后台: http://your-domain/admin"
echo "2. 在内容管理模块中查看'课程权限管理'菜单"
echo "3. 如果菜单未显示，请："
echo "   - 手动执行 sql/add_course_permission_menu.sql"
echo "   - 清空浏览器缓存"
echo "   - 重新登录管理后台"
echo ""
echo "功能特性："
echo "- 查看用户课程权限申请列表"
echo "- 单个和批量审核权限申请"
echo "- 按课程、用户、状态筛选"
echo "- 查看用户的其他权限申请记录"
echo ""
echo "如有问题，请检查："
echo "- Laravel日志: storage/logs/"
echo "- 数据库连接配置"
echo "- 文件权限设置"
echo "=========================================="
