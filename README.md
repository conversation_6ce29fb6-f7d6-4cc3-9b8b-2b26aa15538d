# RY本地服务平台

## 项目概述
RY本地服务平台是一个提供本地维修、家政等服务的平台，包含用户端和师傅端两个应用，以及管理后台。

## 最新功能更新

### 订单导出功能优化（2025-01-17）
优化了订单管理后台的数据导出功能，提供更完整、格式化的订单信息导出。

#### 优化内容
1. **导出字段增强**
   - 新增用户手机号导出
   - 新增服务分类信息
   - 新增师傅手机号导出
   - 新增服务区域信息
   - 新增预约时间和预约日期
   - 新增各种支付时间节点
   - 新增取消原因和备注信息

2. **数据格式化处理**
   - 支付方式代码转换为可读文本（微信支付、支付宝支付、余额支付）
   - 订单状态数字转换为可读文本（待付款、待接单等）
   - 支付状态代码转换为可读文本（未支付、已支付等）
   - 金额格式化为两位小数
   - 时间格式化为标准格式（Y-m-d H:i:s）
   - 空值统一显示为'-'

3. **材料费用计算**
   - 自动计算关联材料订单的总金额
   - 排除待付款和已取消状态的材料订单
   - 确保材料费用计算的准确性

4. **导出选项配置**
   - 设置导出格式为XLSX
   - 禁用"导出所有"选项，避免大数据量导出问题
   - 保留"导出当前页"和"导出选中行"选项
   - 文件名自动包含时间戳

#### 技术实现
- 使用Dcat Admin的`rows()`方法处理导出数据
- 通过关联查询获取完整的订单信息
- 实现数据转换和格式化逻辑
- 确保导出性能和数据准确性

### 时区处理优化（2025-01-17）
系统修复了订单API中的时区转换问题，确保前端显示的时间与数据库存储时间保持一致。

#### 问题背景
- 数据库存储时间：`2025-06-03 09:36:10`
- API返回时间：`2025-06-03T01:36:10.000000Z`（存在8小时时差）

#### 解决方案
- 在OrderController中添加`formatDateTime`方法
- 使用Carbon类进行精确的时区转换
- 确保API返回本地时区时间格式（Asia/Shanghai）
- 统一所有时间字段的格式化处理

#### 技术细节
- 保留Dcat Admin的HasDateTimeFormatter trait以兼容后台管理
- 仅在API层面进行时间格式化处理
- 返回标准'Y-m-d H:i:s'格式，无UTC时区后缀
- 支持订单创建、支付、完成等各类时间字段

### 银行管理模块
系统新增了完整的银行管理功能，为师傅的银行卡管理和提现功能提供基础支持。

#### 功能特性
1. **管理后台银行维护**
   - 银行信息的增删改查
   - 银行图标上传和管理
   - 银行状态控制（启用/禁用）
   - 银行排序设置
   - 使用统计和关联银行卡展示

2. **师傅端银行枚举接口**
   - 获取可用银行列表（按排序显示）
   - 获取银行详情信息
   - 返回银行图标URL

#### 数据库结构
- 新增 `banks` 表，包含字段：
  - `id`: 主键
  - `name`: 银行名称（唯一）
  - `image`: 银行图标路径
  - `sort`: 排序权重
  - `status`: 状态（1启用，0禁用）
  - `created_at/updated_at`: 时间戳
- 更新 `worker_bank_cards` 表，新增 `bank_id` 外键关联

#### API接口
- **师傅端**：
  - `GET /api/worker/banks` - 获取银行列表
  - `GET /api/worker/banks/{id}` - 获取银行详情
- **管理后台**：通过 Dcat Admin 自动路由提供完整的 CRUD 功能

#### 初始数据
系统预置了21家主要银行数据，包括工商银行、建设银行、农业银行等常用银行。

## 系统模块

### 短信服务模块
短信服务模块提供验证码发送功能，支持用户注册、登录、密码重置等场景的手机验证。

#### 已实现功能
1. **阿里云短信服务集成**
   - 支持真实短信发送和开发环境日志模拟
   - 手机号格式验证和发送频率限制
   - 验证码生成、过期控制和安全机制
   
2. **多场景支持**
   - 用户注册验证码
   - 用户登录验证码
   - 密码重置验证码
   - 手机号更新验证码
   
3. **配置灵活**
   - 支持开发/生产环境切换
   - 可配置验证码长度、过期时间、发送间隔
   - 详细的错误处理和日志记录

#### 配置文件
- `config/sms.php` - 短信服务配置
- `env.sms.example` - 环境变量示例
- `docs/sms-service-integration.md` - 详细集成文档

#### 使用说明
详细的配置和使用说明请参考：[短信服务集成文档](docs/sms-service-integration.md)

### 内容管理模块
内容管理模块负责招聘信息、课程管理等内容的管理、展示和互动功能，是系统的增值服务模块。

#### 已实现功能
1. **招聘信息管理**
   - 招聘信息列表展示与搜索
   - 招聘详情查看
   - 简历投递功能
   - 投递记录查询
   
2. **课程管理**
   - 课程多级分类管理
   - 课程列表展示与搜索
   - 课程详情查看
   - 课程学习与进度记录
   - 课程收藏功能
   - 课程权限申请
   - **课程权限管理**（后台管理功能）
     - 申请观看用户列表展示
     - 单个权限申请审核（通过/拒绝）
     - 批量权限申请审核操作
     - 详细筛选和搜索功能
     - 用户权限申请记录查看
     - 完整的审核流程管理
     - **快速跳转功能**：在课程管理页面可直接跳转到对应课程的权限管理页面
       - 通过"权限管理"按钮实现快速跳转
       - 自动过滤显示当前课程的权限申请记录
       - 提供返回课程管理的便捷按钮
       - 显示当前查看课程的标题信息
   
3. **首页内容管理**
   - Banner展示
   - 导航按钮展示

#### API接口列表
1. 获取招聘列表：`GET /api/client/jobs`
2. 获取招聘详情：`GET /api/client/jobs/{id}`
3. 投递简历：`POST /api/client/job-applications`
4. 获取投递记录：`GET /api/client/job-applications`
5. 获取课程分类列表：`GET /api/client/course-categories`
6. 获取课程列表：`GET /api/client/courses`
7. 按分类获取课程：`GET /api/client/courses?category_id={id}`
8. 获取课程详情：`GET /api/client/courses/{id}`
9. 播放/查看课程：`GET /api/client/courses/{id}/view`
10. 记录学习进度：`POST /api/client/courses/{id}/progress`
11. 收藏/取消收藏课程：`POST /api/client/courses/{id}/favorite`
12. 申请观看课程：`POST /api/client/courses/{id}/apply-permission`
13. 获取Banner列表：`GET /api/client/banners`
14. 获取导航按钮列表：`GET /api/client/nav-buttons`
15. 获取我的收藏课程列表：`GET /api/client/my-favorite-courses`
16. 获取我的学习记录：`GET /api/client/my-learning-records`
17. 获取主课程列表：`GET /api/client/main-courses`
18. 获取考试科目列表：`GET /api/client/exam-subjects`
19. 获取热门服务列表：`GET /api/services/hot`
20. 上传图片：`POST /api/client/upload/image`
21. 上传文件：`POST /api/client/upload/file`
22. 删除上传的文件：`POST /api/client/upload/delete`
23. IP位置查询：`GET /api/client/ip/location?ip={ip}` (ip参数可选，不传则使用当前请求IP)
24. 获取推荐服务列表：`GET /api/client/recommendations/services`
25. 获取推荐课程列表：`GET /api/client/recommendations/courses`
26. 获取推荐工作列表：`GET /api/client/recommendations/jobs`
27. 获取所有推荐内容：`GET /api/client/recommendations/all`
28. 获取热门招聘列表：`GET /api/client/hot-jobs`
29. 获取热门课程列表：`GET /api/client/hot-courses`

## 技术架构

### 后端
- PHP 8.1
- Laravel 10.x
- MySQL 8.0

### 前端
- Vue 3
- Uniapp (小程序端)

## 请求日志系统

系统集成了完善的请求日志记录功能，可记录所有API请求的详细信息。

### 日志记录内容
- 请求时间戳
- 请求路由和URI
- 请求方法(GET/POST等)
- 请求参数(自动过滤敏感信息)
- 用户ID和IP地址
- User-Agent信息
- 响应状态码
- 请求处理时间
- 请求来源(Referer)

### 日志存储结构
日志文件按年/月/日/小时的目录结构自动存储，方便查询和管理：
```
storage/logs/requests/2023/12/25/10/request_log.log
```

### 日志配置
可通过`config/request_logging.php`配置文件自定义日志行为：
- 设置日志存储路径
- 开启/关闭请求日志记录
- 排除特定路由或请求方法
- 定义敏感信息字段
- 设置日志保留时间

### 日志清理
系统会自动清理过期日志文件，可通过配置文件设置保留时间，默认保留30天。
清理命令：
```
php artisan logs:clean [--days=30]
```

## 接口规范
所有API接口统一返回JSON格式数据，格式如下：

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}
```

### 错误码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权访问
- 403: 禁止访问
- 404: 资源未找到
- 422: 验证失败
- 500: 服务器内部错误

### 统一响应服务
系统使用`ResponseService`类处理所有API响应，确保响应格式一致。

## 项目安装与配置

### 环境要求
- PHP >= 8.1
- Composer
- MySQL >= 8.0
- Node.js >= 14

### 安装步骤
1. 克隆项目代码
```
git clone [项目地址]
```

2. 安装依赖
```
composer install
```

3. 配置环境变量
```
cp .env.example .env
```

4. 生成应用密钥
```
php artisan key:generate
```

5. 配置数据库并运行迁移
```
php artisan migrate
```

6. 启动服务
```
php artisan serve
```

## 技术栈

### 后端
- PHP 8.1+
- Laravel 8.x
- MySQL 5.7+
- Redis
- RESTful API
- JSON API规范
- Swagger/OpenAPI文档

## 主要功能模块

系统包含以下主要功能模块：

1. **用户认证模块** - 用户注册、登录、个人信息管理
   - 用户基本信息管理
   - 用户地址管理（支持多地址，设置默认）
   - 手机号绑定与认证
   
2. **服务管理模块** - 服务项目管理、分类和展示

3. **订单管理模块** - 订单创建、派单、状态流转
   - 下单流程（选择地址、预约时间）
   - 支付流程（预约费、服务费）
   - 订单状态流转管理
   - 管理后台订单操作功能：
     - 【待接单】状态：支持分派师傅和取消订单
     - 【待师傅上门】状态：支持分派其他师傅和取消订单
     - 自动处理预约费退款和状态日志记录
   
4. **支付模块** - 支付管理、退款管理、充值功能
   - 支持微信支付、支付宝支付、余额支付
   - 多种支付类型：预约费、服务费、材料费、充值

5. **师傅管理模块** - 师傅注册认证、接单管理
   - 师傅认证审核流程
   - 师傅技能管理
   - 师傅等级管理
   - 师傅佣金流水管理

6. **师傅佣金流水管理** - 师傅收益记录管理
   - 佣金流水查询和统计
   - 支持多种收益类型：订单佣金、押金返还、其他收益
   - 实时统计：总佣金、今日佣金、本月佣金
   - 数据导出功能（Excel格式）
   - 师傅和订单关联跳转
   - 手动添加佣金记录（自动更新师傅余额）
   - 详细的筛选和搜索功能
   - 只读模式确保数据安全性

7. **材料管理模块** - 材料分类和材料商品管理
   - 材料分类树状表格管理（支持3级分类）
   - 支持分页和点击加载的层级展示
   - 拖拽排序和快捷编辑功能
   - 材料商品管理和购物车功能
   - 使用EasyWeChat 5.0版本处理微信支付
   - 支持统一下单、订单查询、支付回调、退款申请等完整支付流程
   - 完整的退款流程和状态查询
   - 支付回调处理和安全验证

8. **内容管理模块** - 招聘信息、课程管理

9. **营销管理模块** - 营销活动、优惠券、会员套餐

10. **统计分析模块** - 各类数据统计和分析

11. **系统管理模块** - 权限管理、参数配置

12. **位置服务模块** - IP定位服务，区域查询，地址解析

系统架构概览请参考：[系统架构概览](docs/system-module/00-系统架构概览.md)

### 前端应用
1. **用户端** - 小程序/H5应用
2. **师傅端** - 小程序/H5应用
3. **管理后台** - PC管理系统

## API文档

### 用户端API
1. [用户认证API](docs/api/user/auth_api.md)
2. [服务管理API](docs/api/client/service_api.md)
3. [订单管理API](docs/api/user/order_api.md)
4. [支付相关API](docs/api/user/payment_api.md)
5. [招聘大厅API](docs/api/client/job_api.md)
6. [课程学习API](docs/api/client/course_api.md)
7. [内容推荐API](docs/api/client/recommendations.md)
8. [技能管理API](docs/api/skill_api.md)
9. [预约时间段API](docs/api/appointment_timeSlot_api.md)

### 师傅端API
1. [师傅认证API](docs/api/worker/auth_api.md)
2. [师傅订单API](docs/api/worker/order_api.md)
3. [师傅收益API](docs/api/worker/income_api.md)
4. [师傅资质API](docs/api/worker/qualification_api.md)

### 管理后台API
1. [管理员认证API](docs/api/admin/auth_api.md)
2. [用户管理API](docs/api/admin/user_api.md)
3. [师傅管理API](docs/api/admin/worker_api.md)
4. [订单管理API](docs/api/admin/order_api.md)
5. [内容管理API](docs/api/admin/content_api.md)

## 开发指南

### 代码规范
- 遵循PSR-12编码规范
- 使用严格类型声明：`declare(strict_types=1);`
- 采用依赖注入和服务容器
- API响应格式统一

### 数据库设计
- 使用迁移文件管理数据库结构
- 实现适当的索引提高查询性能
- 遵循数据库命名规范

### API开发规范
- 遵循RESTful设计原则
- 使用适当的HTTP状态码
- 实现API版本控制
- 规范化错误响应格式

## 测试
- 单元测试
- API功能测试
- 负载测试

## 贡献指南
欢迎提交Issue和Pull Request，请确保遵循项目的代码规范和提交规范。

## 许可证
本项目采用[MIT许可证](LICENSE)。

## 项目特点

- 遵循 Laravel 和 PHP 最佳实践
- 使用 RESTful API 设计规范
- 实现完整的订单流程管理
- 集成支付功能
- 多角色用户系统（用户、师傅、管理员）

## 系统工具

### 省市区数据导入工具

系统提供了中国省市区数据导入工具，可以将不同格式的省市区JSON数据导入到系统中。

#### 功能特点
- 支持多种JSON数据格式的处理与转换
- 提供批量导入和SQL生成功能
- 支持保留原始ID或自动生成新ID
- 提供直接执行SQL的选项

#### 使用方法
```bash
# 处理JSON数据格式（如果需要）
php resources/scripts/process_areas_data.php 原始数据.json 标准格式数据.json

# 生成SQL文件
php artisan import:areas 标准格式数据.json

# 生成SQL并直接执行导入
php artisan import:areas 标准格式数据.json --execute

# 保留原始ID
php artisan import:areas 标准格式数据.json --keep-ids
```

详细使用说明请参考：[省市区数据导入指南](docs/areas_import_guide.md)

## 数据库表结构

详见 `sql` 目录下的SQL文件

## 系统参数模块

系统参数模块提供了一个统一的系统配置管理机制，支持多种类型参数的存储和读取。

### 主要功能

1. 参数分组管理：支持将参数按功能分组，便于管理
2. 多种参数类型：支持文本、文本域、图片、开关、下拉框、数字、评分等多种参数类型
3. 缓存机制：参数读取支持缓存，提高性能
4. 助手函数：提供了便捷的助手函数用于获取参数

### 如何使用

**获取单个参数**

```php
// 使用助手函数
$siteName = system_param('site_name', '默认站点名');

// 或者使用服务类
$systemParamService = app(\App\Services\SystemParamService::class);
$siteName = $systemParamService->getValue('site_name', '默认站点名');
```

**获取指定分组的所有参数**

```php
// 使用助手函数
$basicParams = system_params('basic');

// 或者使用服务类
$systemParamService = app(\App\Services\SystemParamService::class);
$basicParams = $systemParamService->getGroupParams('basic');
```

**在视图中使用**

通过中间件，基础配置参数会自动共享到所有视图中：

```html
<h1>{{ $system['site_name'] ?? '默认站点名' }}</h1>
```

### 参数类型

系统参数支持以下类型：

- `text`: 文本框
- `textarea`: 文本域
- `image`: 图片
- `switch`: 开关
- `select`: 下拉框
- `number`: 数字
- `rate`: 评分

### 参数分组

系统参数支持以下分组：

- `basic`: 基础配置
- `payment`: 支付配置
- `sms`: 短信配置
- `wechat`: 微信配置
- `app`: APP配置
- `other`: 其他配置

## 支付模块配置

### 微信支付配置

项目使用 `w7corp/easywechat` 6.0 版本进行微信支付集成。

#### 环境变量配置

在 `.env` 文件中添加以下微信支付配置：

```env
# 微信支付配置
WECHAT_PAYMENT_APPID=your_app_id
WECHAT_PAYMENT_MCH_ID=your_merchant_id
WECHAT_PAYMENT_KEY=your_api_key
WECHAT_PAYMENT_CERT_PATH=/path/to/apiclient_cert.pem
WECHAT_PAYMENT_KEY_PATH=/path/to/apiclient_key.pem
WECHAT_PAYMENT_NOTIFY_URL=https://your-domain.com/api/user/payment/callback
WECHAT_PAYMENT_SANDBOX=false
```

#### 配置说明

- `WECHAT_PAYMENT_APPID`：微信公众号或小程序的AppID
- `WECHAT_PAYMENT_MCH_ID`：微信支付商户号
- `WECHAT_PAYMENT_KEY`：微信支付API密钥（在商户平台设置）
- `WECHAT_PAYMENT_CERT_PATH`：API证书文件路径（退款时需要）
- `WECHAT_PAYMENT_KEY_PATH`：API私钥文件路径（退款时需要）
- `WECHAT_PAYMENT_NOTIFY_URL`：支付结果通知地址
- `WECHAT_PAYMENT_SANDBOX`：是否使用沙箱环境（测试时设为true）

#### 证书文件配置

1. 从微信商户平台下载API证书
2. 将证书文件放置在安全目录（建议放在项目根目录外）
3. 确保Web服务器有读取权限
4. 在环境变量中配置正确的绝对路径

#### 支付流程

1. **创建支付订单**
   ```php
   POST /api/user/payment/create
   ```

2. **查询支付状态**
   ```php
   GET /api/user/payment/status?payment_no=xxx
   ```

3. **支付回调处理**
   ```php
   POST /api/user/payment/callback
   ```

4. **申请退款**（管理后台功能）
   ```php
   POST /api/admin/payment/refund
   ```

#### 支付类型

- `appointment`：预约费支付
- `service`：服务费支付
- `material`：材料费支付
- `recharge`：账户充值
- `final`：尾款支付

#### 支付方式

- `wechat`：微信支付
- `alipay`：支付宝支付
- `balance`：余额支付

### 安全注意事项

1. **API密钥安全**：确保API密钥不被泄露，定期更换
2. **证书保护**：API证书文件权限设置为600，仅允许应用读取
3. **回调验证**：严格验证支付回调的签名和来源
4. **HTTPS传输**：生产环境必须使用HTTPS协议
5. **日志记录**：记录所有支付相关操作的详细日志

### 故障排除

#### 常见问题

1. **证书路径错误**
   - 检查证书文件是否存在
   - 确认路径为绝对路径
   - 验证文件权限设置

2. **签名验证失败**
   - 检查API密钥是否正确
   - 确认参数编码格式
   - 验证签名算法实现

3. **回调接收失败**
   - 检查回调URL是否可访问
   - 确认服务器防火墙设置
   - 验证SSL证书有效性

#### 调试方法

1. 查看支付日志：`storage/logs/payment.log`
2. 检查Laravel日志：`storage/logs/laravel.log`
3. 使用微信支付沙箱环境进行测试
4. 通过微信商户平台查看交易记录

## 师傅审核功能

系统提供完整的师傅认证审核功能，帮助平台管理员高效管理师傅资质。

### 审核流程

1. 师傅注册并提交认证资料（身份证、技能证书等）
2. 管理员在后台进行审核：
   - 单个审核：点击审核按钮，查看详细资料后通过或拒绝
   - 批量审核：选中多个待审核师傅，一键批量通过

### 审核状态

- **待审核** - 师傅已提交资料，等待管理员审核
- **已通过** - 管理员已审核通过，师傅可以正常接单
- **已拒绝** - 管理员已拒绝认证，需要师傅重新提交资料

### 使用方法

1. 进入管理后台 -> 师傅管理
2. 可以看到所有师傅列表，包含认证状态
3. 对于待审核的师傅，有"审核"按钮
4. 点击"审核"按钮，进入审核页面查看详细资料
5. 选择"通过"或"拒绝"，如选择拒绝需填写拒绝原因
6. 也可选中多个待审核师傅，点击"批量审核通过"按钮快速审核

## 新增功能：考试科目管理

考试科目管理功能位于内容管理模块下，用于管理课程相关的考试科目。

### 功能特点
- 支持多级科目分类（父子关系）
- 使用树状表格展示科目层级结构
- 支持科目排序
- 与课程系统关联

### 使用方法
1. 进入管理后台，点击"内容管理">"考试科目管理"
2. 可以添加、编辑、删除科目
3. 科目可以设置上级科目，形成多级结构
4. 在课程管理中可以选择关联的考试科目

### 开发说明
- 使用Dcat Admin的ModelTree特性实现树状表格
- 支持自动计算科目层级
- 树状表格支持按需加载子节点

## 材料分类管理功能

材料分类管理功能采用Dcat Admin的树状表格展示，提供直观的层级管理体验。

### 功能特点
- **树状表格展示**：基于Dcat Admin的ModelTree trait实现
- **三级分类支持**：支持最多3级分类结构（一级→二级→三级）
- **分页和点击加载**：适合大数据量的分类管理
- **拖拽排序**：支持同级分类的拖拽排序功能
- **快捷搜索**：支持按分类名称和ID进行快速搜索
- **层级徽章显示**：不同层级使用不同颜色标识（一级-蓝色，二级-绿色，三级-橙色）
- **快捷编辑**：支持行内快速编辑分类信息
- **数据导出**：支持将分类数据导出为Excel文件

### 使用方法
1. 进入管理后台，点击"材料管理">"材料分类"
2. 查看树状表格展示的分类层级结构
3. 点击分类名称前的展开/收起图标查看子分类
4. 使用搜索功能快速定位特定分类
5. 拖拽分类行进行排序调整
6. 点击快捷编辑按钮修改分类信息

### 技术实现
- **模型配置**：MaterialCategory模型使用ModelTree trait
- **字段映射**：titleColumn='name', orderColumn='sort', parentColumn='parent_id'
- **控制器优化**：移除原有分栏布局，改为单一树状表格
- **搜索功能**：支持按ID和名称的快捷搜索，取消只查顶级数据的限制
- **过滤器**：保留分类名称、层级和状态的筛选功能

### 与模型树的区别
- **模型树**：适合数据量较小的场景，支持拖拽操作，一次性加载所有数据
- **树状表格**：适合数据量较大的场景，支持分页和点击加载，但不支持拖拽层级操作

### 数据结构要求
```sql
CREATE TABLE `material_categories` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) UNSIGNED DEFAULT '0' COMMENT '父级分类ID',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '层级',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `material_categories_parent_id_index` (`parent_id`)
);
```

## 师傅端权限控制

### 师傅状态说明
- **待审核 (STATUS_PENDING = 0)**: 师傅刚注册或修改信息后，等待管理员审核
- **已通过 (STATUS_APPROVED = 1)**: 师傅审核通过，可以正常使用所有功能
- **已拒绝 (STATUS_REJECTED = 2)**: 师傅审核被拒绝，但仍可登录和使用基本功能

### 登录权限
- **所有状态的师傅都可以正常登录**，包括被拒绝状态的师傅
- 登录后会根据审核状态显示相应的提示信息
- 驳回状态的师傅可以修改个人信息后重新提交审核

### 功能权限
- **待审核状态**: 只能查看个人信息、修改密码、退出登录等基本功能
- **已通过状态**: 可以使用所有功能，包括接单、查看订单大厅等
- **已拒绝状态**: 可以使用大部分功能，并可以修改个人信息重新提交审核

### 中间件说明
- `worker.auth`: 验证师傅是否已登录
- `worker.approval`: 检查师傅审核状态，限制待审核师傅的功能访问

## 应用类型
1. **用户端** - 小程序/H5应用
2. **师傅端** - 小程序/H5应用
3. **管理后台** - Dcat Admin

## 项目特点

- 遵循 Laravel 和 PHP 最佳实践
- 使用 RESTful API 设计规范
- 实现完整的订单流程管理
- 集成支付功能
- 多角色用户系统（用户、师傅、管理员）
- 灵活的权限控制系统

## 最近更新

### 2025-01-23 - 优化师傅登录权限控制
- 允许驳回状态的师傅正常登录
- 优化权限控制中间件，只限制待审核状态师傅的功能访问
- 完善状态提示信息，提供更好的用户体验
- 支持驳回状态师傅修改信息后自动重新提交审核

## 支付系统

### 支付方式
系统支持以下支付方式：
- **微信支付** - 支持扫码支付（Native）和H5支付
- **支付宝支付** - 支持扫码支付和H5支付
- **余额支付** - 使用钱包余额支付

### 支付类型
- **预约费支付（appointment）** - 用户下单时支付的预约费
- **服务费支付（service）** - 师傅设定费用后的服务费支付
- **尾款支付（final）** - 服务完成后的尾款支付
- **材料费支付（material）** - 材料购买费用
- **充值支付（recharge）** - 钱包充值功能

### 充值功能

#### 充值方式
1. **活动充值** - 根据预设的充值活动进行充值，可享受赠送金额
2. **自定义充值** - 用户自定义充值金额，支持1-50000元

#### 充值流程
1. 用户选择充值方式（活动充值或自定义充值）
2. 选择支付方式（微信支付、支付宝、余额支付）
3. 系统创建充值记录和支付记录
4. 调用相应的支付网关创建支付订单
5. 用户完成支付
6. 支付网关回调通知
7. 系统更新充值状态，增加用户钱包余额

#### 充值活动
- 支持设置充值赠送活动
- 可配置活动时间范围
- 支持启用/禁用状态管理

#### API接口

**充值接口**
```
POST /api/user/recharge
```
参数说明：
- `custom_amount`: 自定义充值金额（可选）
- `recharge_activity_id`: 充值活动ID（可选）
- `payment_method`: 支付方式（wechat/alipay/balance）

**充值记录查询**
```
GET /api/user/recharge/records
```

**充值支付状态查询**
```
GET /api/user/recharge/payment-status
```
参数：`recharge_id` - 充值记录ID

**获取充值活动列表**
```
GET /api/user/recharge/activities
```

**钱包信息查询**
```
GET /api/user/wallet/info
```

**钱包交易记录**
```
GET /api/user/wallet/transactions
```

### 微信支付集成

#### 配置要求
在 `config/wechat.php` 中配置微信支付参数：
```php
'payment' => [
    'default' => [
        'app_id' => env('WECHAT_PAYMENT_APPID'),
        'mch_id' => env('WECHAT_PAYMENT_MCH_ID'),
        'key' => env('WECHAT_PAYMENT_KEY'),
        'cert_path' => env('WECHAT_PAYMENT_CERT_PATH'),
        'key_path' => env('WECHAT_PAYMENT_KEY_PATH'),
        'notify_url' => env('WECHAT_PAYMENT_NOTIFY_URL'),
    ],
],
```

#### 环境变量配置
在 `.env` 文件中添加：
```
WECHAT_PAYMENT_APPID=your_app_id
WECHAT_PAYMENT_MCH_ID=your_mch_id
WECHAT_PAYMENT_KEY=your_api_key
WECHAT_PAYMENT_NOTIFY_URL=https://yourdomain.com/api/payment/callback
```

#### 支付回调
微信支付回调统一由 `PaymentController@callback` 处理，支持：
- 签名验证
- 重复通知处理
- 业务状态更新
- 日志记录

### 钱包系统

#### 钱包功能
- 余额管理
- 冻结余额管理
- 交易记录
- 收支统计

#### 交易类型
- **TYPE_RECHARGE** - 充值
- **TYPE_CONSUME** - 消费
- **TYPE_REFUND** - 退款
- **TYPE_WITHDRAWAL** - 提现

## 订单系统

### 订单状态流转
```
创建订单 → 待付款 → 待接单 → 待师傅上门 → 待设定服务费用 → 待维修 → 待维修确认 → 已完成
```

### 订单状态常量
- STATUS_PENDING_PAYMENT = 0 (待付款)
- STATUS_PENDING_ACCEPT = 1 (待接单)
- STATUS_PENDING_VISIT = 2 (待师傅上门)
- STATUS_PENDING_FEE = 3 (待设定服务费用)
- STATUS_PENDING_SERVICE = 4 (待维修)
- STATUS_PENDING_CONFIRM = 5 (待维修确认)
- STATUS_COMPLETED = 6 (已完成)
- STATUS_CANCELLED = 7 (已取消)

## 应用类型
1. **用户端** - 小程序/H5应用
2. **师傅端** - 小程序/H5应用
3. **管理后台** - Dcat Admin

## 项目特点

- 遵循 Laravel 和 PHP 最佳实践
- 使用 RESTful API 设计规范
- 实现完整的订单流程管理
- 集成支付功能
- 多角色用户系统（用户、师傅、管理员）
- 完整的钱包充值系统
- 支持充值活动和赠送机制

## 安装和配置

### 环境要求
- PHP >= 8.1
- Composer
- MySQL >= 8.0
- Redis（可选）

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd RYLocalService
```

2. 安装依赖
```bash
composer install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和微信支付参数
```

4. 生成应用密钥
```bash
php artisan key:generate
```

5. 运行数据库迁移
```bash
php artisan migrate
```

6. 填充测试数据（可选）
```bash
php artisan db:seed
```

### 微信支付配置

1. 在微信商户平台获取相关参数
2. 配置支付回调地址
3. 上传商户证书文件
4. 在 `.env` 文件中配置相关参数

### 测试

运行测试套件：
```bash
php artisan test
```

## API文档

详细的API文档请参考 `docs/api/` 目录下的文档文件。

## 贡献

欢迎提交Issue和Pull Request来帮助改进项目。

## 许可证

本项目采用MIT许可证。

## 优惠券模块功能

### 新增文本字段功能
优惠券模型现在支持以下文本字段，可以直接在API中返回：

#### 1. 类型文本 (type_text)
- `全场通用` - 适用于所有订单类型
- `仅限材料` - 仅适用于材料订单
- `仅限维修服务` - 仅适用于维修服务订单

#### 2. 折扣类型文本 (discount_type_text)
- `固定金额减免` - 固定金额折扣
- `百分比折扣` - 按比例折扣

#### 3. 有效期文本 (validity_period_text)
- 自动格式化显示有效期，如：
  - `2024-01-01 至 2024-12-31`
  - `截至2024-12-31有效`
  - `永久有效`
  - `(已过期)` 或 `(未开始)` 状态提示

#### 4. 折扣值文本 (discount_value_text)
- `减免50元` - 固定金额显示
- `8折` - 折扣比例显示

#### 5. 使用条件文本 (condition_text)
- 组合显示类型和金额条件，如：`全场通用，满100元可用`

#### 6. 完整条件标题 (condition_title)
- 兼容旧版本的完整条件描述
- 包含类型、金额条件和有效期
- 如：`全场通用，满100元可用，2024-01-01至2024-12-31有效`

### API使用方式
```json
{
  "id": 1,
  "name": "新用户专享8折优惠券",
  "type": "all",
  "discount_type": "percent",
  "discount_value": "0.80",
  "min_amount": "100.00",
  "start_time": "2024-01-01 00:00:00",
  "end_time": "2024-12-31 23:59:59",
  "status": true,
  "type_text": "全场通用",
  "discount_type_text": "百分比折扣",
  "discount_value_text": "8折",
  "validity_period_text": "2024-01-01 至 2024-12-31",
  "condition_text": "全场通用，满100元可用",
  "condition_title": "全场通用，满100元可用，2024-01-01至2024-12-31有效"
}
```

这些文本字段通过Laravel的模型访问器自动生成，无需手动格式化，提高了前端展示的便利性。
