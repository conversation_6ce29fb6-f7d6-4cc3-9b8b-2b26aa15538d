# 师傅端微信登录功能设计文档

## 1. 概述

师傅端微信登录功能旨在为师傅用户提供便捷的微信登录方式，同时保持师傅端特有的实名认证和技能审核流程。本功能参考用户端微信登录实现，针对师傅端的特殊需求进行了适配。

## 2. 功能特点

### 2.1 核心功能
- **微信静默登录**：已绑定师傅可直接登录
- **微信授权登录**：获取微信用户信息，检查绑定状态
- **微信绑定注册**：新师傅通过微信完成注册流程
- **微信绑定手机号**：已登录师傅绑定/更新手机号

### 2.2 特殊考虑
- 师傅注册需要实名认证和技能认证
- 支持师傅审核状态的处理
- 保持与现有手机号登录的兼容性
- 完整的登录日志记录

## 3. 系统架构

### 3.1 核心组件
```
师傅端微信登录系统
├── WechatWorkerLoginService    # 师傅端微信登录服务
├── Worker模型扩展             # 添加微信相关字段
├── WorkerAuthController扩展    # 添加微信登录接口
├── WechatLoginLog扩展          # 支持师傅端日志
└── 数据库迁移                 # 添加必要字段
```

### 3.2 依赖关系
- 复用现有的WechatLoginService基础方法
- 扩展现有的微信配置和工具类
- 集成现有的JWT认证系统

## 4. 数据库设计

### 4.1 Worker表扩展
需要添加以下微信相关字段：

```sql
ALTER TABLE workers ADD COLUMN unionid VARCHAR(128) NULL COMMENT '微信UnionID';
ALTER TABLE workers ADD COLUMN session_key VARCHAR(128) NULL COMMENT '微信SessionKey';
ALTER TABLE workers ADD COLUMN wechat_nickname VARCHAR(100) NULL COMMENT '微信昵称';
ALTER TABLE workers ADD COLUMN wechat_avatar VARCHAR(500) NULL COMMENT '微信头像';
ALTER TABLE workers ADD COLUMN platform VARCHAR(20) NULL COMMENT '注册平台';
ALTER TABLE workers ADD COLUMN last_login_platform VARCHAR(20) NULL COMMENT '最后登录平台';
ALTER TABLE workers ADD COLUMN last_login_at TIMESTAMP NULL COMMENT '最后登录时间';

-- 添加索引
ALTER TABLE workers ADD INDEX idx_workers_unionid (unionid);
ALTER TABLE workers ADD INDEX idx_workers_openid (openid);
```

### 4.2 WechatLoginLog表扩展
添加师傅端支持：

```sql
ALTER TABLE wechat_login_logs ADD COLUMN worker_id BIGINT UNSIGNED NULL COMMENT '师傅ID';
ALTER TABLE wechat_login_logs ADD INDEX idx_wechat_login_logs_worker_id (worker_id);
```

## 5. API接口设计

### 5.1 微信静默登录
```
POST /api/worker/wechat/silent-login
```

**请求参数：**
```json
{
  "code": "string, required, 微信授权码"
}
```

**响应示例：**
```json
// 成功登录
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 604800,
    "worker": {
      "id": 1,
      "name": "张师傅",
      "phone": "13800138000",
      "status": 1,
      "status_message": "您的账号已通过审核，可以正常使用所有功能"
    }
  }
}

// 需要授权登录
{
  "code": 202,
  "message": "需要授权登录",
  "data": {
    "openid": "ox1c...",
    "unionid": "oV2K...",
    "need_auth": true
  }
}
```

### 5.2 微信授权登录
```
POST /api/worker/wechat/authorized-login
```

**请求参数：**
```json
{
  "code": "string, required, 微信授权码",
  "userInfo": {
    "nickName": "string, required, 微信昵称",
    "avatarUrl": "string, optional, 微信头像"
  }
}
```

**响应示例：**
```json
// 已绑定，直接登录
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 604800,
    "worker": { /* 师傅信息 */ }
  }
}

// 需要注册
{
  "code": 203,
  "message": "需要注册师傅账号",
  "data": {
    "openid": "ox1c...",
    "unionid": "oV2K...",
    "user_info": {
      "nickname": "微信用户",
      "avatar": "https://..."
    },
    "need_register": true
  }
}
```

### 5.3 微信绑定注册
```
POST /api/worker/wechat/bind-register
```

**请求参数：**
```json
{
  "code": "string, required, 微信授权码",
  "userInfo": {
    "nickName": "string, required, 微信昵称",
    "avatarUrl": "string, optional, 微信头像"
  },
  // 师傅注册信息
  "name": "string, required, 真实姓名",
  "phone": "string, required, 手机号",
  "password": "string, required, 密码",
  "confirm_password": "string, required, 确认密码",
  "id_card": "string, required, 身份证号",
  "id_card_front": "string, required, 身份证正面",
  "id_card_back": "string, required, 身份证背面",
  "code": "string, required, 短信验证码",
  "province_id": "number, required, 省份ID",
  "city_id": "number, required, 城市ID",
  "district_id": "number, required, 区县ID",
  "skill_ids": "array, required, 技能ID数组",
  "certificate_images": "string, required, 技能证书图片"
}
```

### 5.4 微信绑定手机号
```
POST /api/worker/wechat/bind-phone
```

**请求参数：**
```json
{
  "phoneCode": "string, required, 手机号授权码"
}
```

## 6. 服务类设计

### 6.1 WechatWorkerLoginService

```php
<?php

namespace App\Services;

class WechatWorkerLoginService
{
    /**
     * 师傅微信静默登录
     */
    public function silentLogin(string $code): array
    
    /**
     * 师傅微信授权登录
     */
    public function authorizedLogin(string $code, array $userInfo): array
    
    /**
     * 师傅微信绑定注册
     */
    public function bindRegister(string $code, array $userInfo, array $workerData): array
    
    /**
     * 师傅微信绑定手机号
     */
    public function bindPhone(string $phoneCode, int $workerId): array
    
    /**
     * 查找师傅通过微信数据
     */
    private function findWorkerByWechatData(array $wechatData): ?Worker
    
    /**
     * 创建师傅账号
     */
    private function createWorkerAccount(array $wechatData, array $workerData): Worker
    
    /**
     * 更新师傅登录信息
     */
    private function updateWorkerLoginInfo(Worker $worker, array $wechatData): void
}
```

## 7. 登录流程设计

### 7.1 首次微信登录流程
```mermaid
graph TD
    A[师傅点击微信登录] --> B[获取微信授权码]
    B --> C[静默登录]
    C --> D{是否已绑定}
    D -->|是| E[直接登录成功]
    D -->|否| F[提示需要授权]
    F --> G[师傅确认授权]
    G --> H[获取微信用户信息]
    H --> I{是否已注册}
    I -->|是| E
    I -->|否| J[显示注册表单]
    J --> K[填写师傅信息]
    K --> L[提交注册]
    L --> M[创建师傅账号]
    M --> N[等待审核]
```

### 7.2 已绑定师傅登录流程
```mermaid
graph TD
    A[师傅点击微信登录] --> B[获取微信授权码]
    B --> C[静默登录]
    C --> D[验证师傅状态]
    D --> E{审核状态}
    E -->|已通过| F[正常登录]
    E -->|待审核| G[登录成功，显示审核提示]
    E -->|已拒绝| H[登录成功，显示拒绝原因]
    F --> I[返回Token和师傅信息]
    G --> I
    H --> I
```

## 8. 安全考虑

### 8.1 数据安全
- 敏感信息加密存储
- 微信授权码防重复使用
- 登录日志完整记录
- 接口请求频率限制

### 8.2 业务安全
- 师傅注册需要实名认证
- 身份证号和手机号唯一性检查
- 技能证书真实性要求
- 审核流程不能绕过

### 8.3 技术安全
- JWT Token安全配置
- API接口权限控制
- 输入参数严格验证
- 错误信息不泄露敏感数据

## 9. 测试方案

### 9.1 单元测试
- WechatWorkerLoginService各方法测试
- Worker模型微信相关方法测试
- 边界条件和异常情况测试

### 9.2 集成测试
- 完整登录流程测试
- 与现有认证系统集成测试
- API接口功能测试

### 9.3 用户验收测试
- 师傅端微信登录体验测试
- 各种设备和环境兼容性测试
- 性能和稳定性测试

## 10. 部署计划

### 10.1 开发阶段
1. 数据库迁移
2. 服务类开发
3. 控制器接口开发
4. 单元测试编写

### 10.2 测试阶段
1. 开发环境测试
2. 集成测试
3. 用户验收测试
4. 性能测试

### 10.3 上线阶段
1. 生产环境部署
2. 数据迁移
3. 功能验证
4. 监控告警配置

## 11. 监控和维护

### 11.1 监控指标
- 微信登录成功率
- 登录响应时间
- 错误日志统计
- 用户使用情况分析

### 11.2 维护计划
- 定期清理登录日志
- 微信API配置更新
- 安全漏洞修复
- 功能优化升级

## 12. 风险评估

### 12.1 技术风险
- 微信API变更风险：**低** - 微信API相对稳定
- 系统集成风险：**中** - 需要与现有认证系统良好集成
- 性能影响风险：**低** - 新增功能不影响现有性能

### 12.2 业务风险
- 用户体验风险：**低** - 提供备选登录方式
- 安全合规风险：**中** - 需要确保符合数据保护要求
- 运营影响风险：**低** - 不影响现有运营流程

## 13. 总结

师傅端微信登录功能的设计充分考虑了师傅端的特殊需求，在保持用户体验的同时确保了安全性和合规性。通过合理的架构设计和完善的测试方案，能够为师傅用户提供便捷、安全的登录体验。 
