# 微信小程序登录兼容方案

## 1. 项目概述

### 1.1 目标
- 兼容微信小程序登录功能
- 实现基于手机号的用户合并机制
- 保持现有API接口的向后兼容性
- 支持多种微信登录方式（H5、小程序、APP）

### 1.2 当前状态分析
- 现有用户表结构支持openid字段
- 已有基础的微信登录功能（仅支持openid验证）
- 缺少unionid字段，无法实现跨平台用户统一
- 缺少微信小程序登录流程支持

## 2. 技术方案设计

### 2.1 数据库结构调整

#### 2.1.1 用户表(users)扩展
需要添加以下字段：

```sql
-- 添加unionid字段和小程序相关字段
ALTER TABLE `users` 
ADD COLUMN `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid' AFTER `openid`,
ADD COLUMN `session_key` varchar(100) DEFAULT NULL COMMENT '小程序session_key' AFTER `unionid`,
ADD COLUMN `wx_nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称' AFTER `session_key`,
ADD COLUMN `wx_avatar` varchar(255) DEFAULT NULL COMMENT '微信头像' AFTER `wx_nickname`,
ADD COLUMN `platform_type` varchar(20) DEFAULT NULL COMMENT '注册平台类型：h5,miniprogram,app' AFTER `wx_avatar`,
ADD COLUMN `merge_source` varchar(20) DEFAULT NULL COMMENT '合并来源：phone,wechat' AFTER `platform_type`,
ADD COLUMN `merged_at` timestamp NULL DEFAULT NULL COMMENT '合并时间' AFTER `merge_source`,
ADD UNIQUE KEY `users_unionid_unique` (`unionid`);
```

#### 2.1.2 用户合并日志表
新建用户合并记录表：

```sql
CREATE TABLE `user_merge_logs` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `main_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '主用户ID',
  `merged_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '被合并用户ID',
  `merge_type` varchar(20) NOT NULL COMMENT '合并类型：phone_bind,wechat_bind',
  `merge_data` json DEFAULT NULL COMMENT '合并数据',
  `operator_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '操作人ID',
  `operator_type` varchar(20) DEFAULT 'user' COMMENT '操作人类型：user,admin,system',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1成功，0失败',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_merge_logs_main_user_id_index` (`main_user_id`),
  KEY `user_merge_logs_merged_user_id_index` (`merged_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户合并日志表';
```

### 2.2 微信小程序登录流程

#### 2.2.1 小程序登录流程图

```mermaid
graph TD
    A[小程序调用wx.login] --> B[获取code]
    B --> C[发送code到后端]
    C --> D[后端调用微信API获取session_key和openid]
    D --> E{是否有unionid?}
    E -->|有| F[根据unionid查找用户]
    E -->|无| G[根据openid查找用户]
    F --> H{用户是否存在?}
    G --> I{用户是否存在?}
    H -->|存在| J[返回用户信息和token]
    H -->|不存在| K[创建新用户]
    I -->|存在| L[返回用户信息和token]
    I -->|不存在| M[引导用户绑定手机号]
    K --> N[返回用户信息和token]
    M --> O[用户输入手机号和验证码]
    O --> P{手机号是否已注册?}
    P -->|已注册| Q[合并用户账号]
    P -->|未注册| R[创建新用户并绑定]
    Q --> S[返回合并后用户信息和token]
    R --> T[返回新用户信息和token]
```

#### 2.2.2 登录接口设计

**新增小程序登录接口：**

```
POST /api/user/miniprogram-login
```

**请求参数：**
```json
{
    "code": "wx_code_from_miniprogram",
    "encrypted_data": "encrypted_phone_data", // 可选，用于获取手机号
    "iv": "initialization_vector", // 可选，配合encrypted_data使用
    "raw_data": "user_info_raw_data", // 可选，用户信息
    "signature": "signature" // 可选，用于验证用户信息
}
```

**响应数据：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "access_token": "jwt_token",
        "token_type": "bearer",
        "expires_in": 604800,
        "user": {
            "id": 1,
            "phone": "13800138000",
            "openid": "o6_bmjrPTlm6_2sgVt7hMZOPfL2M",
            "unionid": "oGRTHuMZNgBk8f4jZUUbgmdj2MRs",
            "nickname": "微信昵称",
            "avatar": "https://example.com/avatar.jpg",
            "platform_type": "miniprogram",
            "need_bind_phone": false // 是否需要绑定手机号
        }
    }
}
```

### 2.3 手机号绑定与用户合并

#### 2.3.1 绑定手机号接口

```
POST /api/user/bind-phone
```

**请求参数：**
```json
{
    "phone": "13800138000",
    "code": "123456",
    "encrypted_data": "encrypted_phone_data", // 小程序获取手机号时使用
    "iv": "initialization_vector" // 小程序获取手机号时使用
}
```

#### 2.3.2 用户合并策略

**合并规则：**
1. **基于unionid合并**：优先级最高，同一个unionid的用户视为同一人
2. **基于手机号合并**：手机号相同的用户可以合并
3. **基于openid合并**：仅在同一平台内有效

**合并逻辑：**
```php
// 合并策略优先级
1. 如果存在unionid，以unionid为主键进行合并
2. 如果不存在unionid，但手机号相同，询问用户是否合并
3. 如果用户同意合并，将微信信息更新到已有账户
4. 如果用户不同意，创建新账户但提示可能存在重复
```

### 2.4 代码实现方案

#### 2.4.1 控制器方法扩展

**扩展AuthController.php：**

```php
/**
 * 微信小程序登录
 * 
 * @param Request $request
 * @return \Illuminate\Http\JsonResponse
 */
public function miniprogramLogin(Request $request)
{
    $validator = Validator::make($request->all(), [
        'code' => 'required|string',
        'encrypted_data' => 'sometimes|string',
        'iv' => 'sometimes|string',
        'raw_data' => 'sometimes|string',
        'signature' => 'sometimes|string',
    ], [
        'code.required' => '微信授权码不能为空',
    ]);

    if ($validator->fails()) {
        return ResponseService::validationError($validator->errors(), '登录失败');
    }

    try {
        // 调用微信小程序API获取用户信息
        $result = $this->getMiniprogramUserInfo($request->code);
        
        if (!$result['success']) {
            return ResponseService::error($result['message'], 422);
        }

        $openid = $result['openid'];
        $unionid = $result['unionid'] ?? null;
        $sessionKey = $result['session_key'];

        // 处理手机号解密
        $phone = null;
        if ($request->has('encrypted_data') && $request->has('iv')) {
            $phone = $this->decryptMiniprogramData(
                $request->encrypted_data, 
                $request->iv, 
                $sessionKey
            );
        }

        // 查找或创建用户
        $user = $this->findOrCreateMiniprogramUser($openid, $unionid, $phone, $sessionKey);

        if (!$user) {
            return ResponseService::error('登录失败，请重试', 500);
        }

        // 检查用户状态
        if ($user->status != 1) {
            return ResponseService::forbidden('账号已被禁用');
        }

        // 更新用户微信信息
        $this->updateUserWechatInfo($user, $request);

        $token = Auth::guard('user')->login($user);

        return $this->respondWithMiniprogramToken($token, $user);

    } catch (\Exception $e) {
        \Log::error('微信小程序登录失败', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return ResponseService::error('登录失败，请重试', 500);
    }
}

/**
 * 绑定手机号
 * 
 * @param Request $request
 * @return \Illuminate\Http\JsonResponse
 */
public function bindPhone(Request $request)
{
    $validator = Validator::make($request->all(), [
        'phone' => 'required|string',
        'code' => 'required|string',
        'encrypted_data' => 'sometimes|string',
        'iv' => 'sometimes|string',
    ], [
        'phone.required' => '手机号不能为空',
        'code.required' => '验证码不能为空',
    ]);

    if ($validator->fails()) {
        return ResponseService::validationError($validator->errors(), '绑定失败');
    }

    $user = Auth::guard('user')->user();
    
    // 验证短信验证码
    if (!$this->verifySmsCode($request->phone, $request->code, 'bind')) {
        return ResponseService::error('验证码错误或已过期', 422);
    }

    // 检查手机号是否已被其他用户使用
    $existingUser = User::where('phone', $request->phone)
        ->where('id', '!=', $user->id)
        ->first();

    if ($existingUser) {
        // 执行用户合并逻辑
        $mergeResult = $this->mergeUsers($user, $existingUser);
        
        if ($mergeResult['success']) {
            return ResponseService::success($mergeResult['user'], '账号合并成功');
        } else {
            return ResponseService::error($mergeResult['message'], 422);
        }
    }

    // 更新用户手机号
    $user->phone = $request->phone;
    $user->merge_source = 'phone';
    $user->merged_at = now();
    $user->save();

    return ResponseService::success($user, '手机号绑定成功');
}

/**
 * 获取微信小程序用户信息
 */
private function getMiniprogramUserInfo($code)
{
    try {
        $config = config('wechat.mini_program.default');
        $app = Factory::miniProgram($config);
        
        $result = $app->auth->session($code);
        
        if (isset($result['errcode']) && $result['errcode'] != 0) {
            return [
                'success' => false,
                'message' => $result['errmsg'] ?? '获取用户信息失败'
            ];
        }

        return [
            'success' => true,
            'openid' => $result['openid'],
            'unionid' => $result['unionid'] ?? null,
            'session_key' => $result['session_key']
        ];

    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => '微信服务异常：' . $e->getMessage()
        ];
    }
}

/**
 * 查找或创建小程序用户
 */
private function findOrCreateMiniprogramUser($openid, $unionid, $phone, $sessionKey)
{
    // 1. 优先根据unionid查找
    if ($unionid) {
        $user = User::where('unionid', $unionid)->first();
        if ($user) {
            // 更新openid和session_key
            $user->openid = $openid;
            $user->session_key = $sessionKey;
            $user->save();
            return $user;
        }
    }

    // 2. 根据openid查找
    $user = User::where('openid', $openid)->first();
    if ($user) {
        // 更新unionid和session_key
        if ($unionid) {
            $user->unionid = $unionid;
        }
        $user->session_key = $sessionKey;
        $user->save();
        return $user;
    }

    // 3. 如果有手机号，根据手机号查找
    if ($phone) {
        $user = User::where('phone', $phone)->first();
        if ($user) {
            // 绑定微信信息到现有账户
            $user->openid = $openid;
            $user->unionid = $unionid;
            $user->session_key = $sessionKey;
            $user->platform_type = 'miniprogram';
            $user->merge_source = 'wechat';
            $user->merged_at = now();
            $user->save();
            return $user;
        }
    }

    // 4. 创建新用户
    $userData = [
        'openid' => $openid,
        'unionid' => $unionid,
        'session_key' => $sessionKey,
        'platform_type' => 'miniprogram',
        'status' => 1,
    ];

    if ($phone) {
        $userData['phone'] = $phone;
        $userData['password'] = Hash::make(Str::random(32)); // 随机密码
    }

    return User::create($userData);
}

/**
 * 合并用户账户
 */
private function mergeUsers($currentUser, $existingUser)
{
    try {
        DB::beginTransaction();

        // 记录合并日志
        UserMergeLog::create([
            'main_user_id' => $existingUser->id,
            'merged_user_id' => $currentUser->id,
            'merge_type' => 'phone_bind',
            'merge_data' => json_encode([
                'current_user' => $currentUser->toArray(),
                'existing_user' => $existingUser->toArray()
            ]),
            'operator_id' => $currentUser->id,
            'operator_type' => 'user',
            'status' => 1,
            'remark' => '小程序绑定手机号自动合并'
        ]);

        // 更新现有用户的微信信息
        $existingUser->openid = $currentUser->openid;
        $existingUser->unionid = $currentUser->unionid;
        $existingUser->session_key = $currentUser->session_key;
        $existingUser->platform_type = 'miniprogram';
        $existingUser->merge_source = 'wechat';
        $existingUser->merged_at = now();
        
        // 合并用户信息（保留较完整的信息）
        if (!$existingUser->nickname && $currentUser->nickname) {
            $existingUser->nickname = $currentUser->nickname;
        }
        if (!$existingUser->avatar && $currentUser->avatar) {
            $existingUser->avatar = $currentUser->avatar;
        }
        
        $existingUser->save();

        // 迁移关联数据（订单、地址、钱包等）
        $this->migrateUserData($currentUser->id, $existingUser->id);

        // 删除或标记删除当前用户
        $currentUser->delete();

        DB::commit();

        return [
            'success' => true,
            'user' => $existingUser,
            'message' => '账号合并成功'
        ];

    } catch (\Exception $e) {
        DB::rollBack();
        
        \Log::error('用户合并失败', [
            'current_user_id' => $currentUser->id,
            'existing_user_id' => $existingUser->id,
            'error' => $e->getMessage()
        ]);

        return [
            'success' => false,
            'message' => '账号合并失败，请联系客服'
        ];
    }
}

/**
 * 迁移用户数据
 */
private function migrateUserData($fromUserId, $toUserId)
{
    // 迁移用户地址
    UserAddress::where('user_id', $fromUserId)->update(['user_id' => $toUserId]);
    
    // 迁移订单
    Order::where('user_id', $fromUserId)->update(['user_id' => $toUserId]);
    
    // 迁移钱包余额
    $fromWallet = UserWallet::where('user_id', $fromUserId)->first();
    $toWallet = UserWallet::firstOrCreate(['user_id' => $toUserId], ['balance' => 0, 'frozen_balance' => 0]);
    
    if ($fromWallet && $fromWallet->balance > 0) {
        $toWallet->balance += $fromWallet->balance;
        $toWallet->save();
        
        // 记录钱包流水
        WalletTransaction::create([
            'user_id' => $toUserId,
            'amount' => $fromWallet->balance,
            'before_balance' => $toWallet->balance - $fromWallet->balance,
            'after_balance' => $toWallet->balance,
            'type' => 'merge',
            'related_id' => $fromUserId,
            'remark' => '账号合并转入余额'
        ]);
    }
    
    // 迁移优惠券
    UserCoupon::where('user_id', $fromUserId)->update(['user_id' => $toUserId]);
    
    // 迁移其他关联数据...
}
```

#### 2.4.2 路由配置

```php
// routes/api.php
Route::prefix('user')->group(function () {
    Route::post('miniprogram-login', [AuthController::class, 'miniprogramLogin']);
    Route::post('bind-phone', [AuthController::class, 'bindPhone'])->middleware('user.auth');
    
    // 保持现有路由不变
    Route::post('login', [AuthController::class, 'login']);
    Route::post('wx-login', [AuthController::class, 'wxLogin']);
    Route::post('register', [AuthController::class, 'register']);
    // ...其他路由
});
```

### 2.5 前端集成指南

#### 2.5.1 小程序端集成

```javascript
// 小程序登录流程
const loginWithMiniprogram = async () => {
  try {
    // 1. 获取登录凭证
    const loginResult = await wx.login();
    if (!loginResult.code) {
      throw new Error('获取登录凭证失败');
    }

    // 2. 尝试获取用户手机号（需要用户授权）
    let phoneData = null;
    try {
      const phoneResult = await wx.getPhoneNumber();
      phoneData = {
        encrypted_data: phoneResult.encryptedData,
        iv: phoneResult.iv
      };
    } catch (e) {
      console.log('用户未授权手机号');
    }

    // 3. 调用后端登录接口
    const response = await wx.request({
      url: '/api/user/miniprogram-login',
      method: 'POST',
      data: {
        code: loginResult.code,
        ...phoneData
      }
    });

    if (response.data.code === 0) {
      // 登录成功，保存token
      wx.setStorageSync('token', response.data.data.access_token);
      wx.setStorageSync('user', response.data.data.user);
      
      // 检查是否需要绑定手机号
      if (response.data.data.user.need_bind_phone) {
        // 引导用户绑定手机号
        wx.navigateTo({
          url: '/pages/bind-phone/index'
        });
      } else {
        // 登录完成，跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    } else {
      throw new Error(response.data.message);
    }

  } catch (error) {
    console.error('登录失败:', error);
    wx.showToast({
      title: '登录失败',
      icon: 'error'
    });
  }
};

// 绑定手机号
const bindPhone = async (phone, code) => {
  try {
    const response = await wx.request({
      url: '/api/user/bind-phone',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      data: {
        phone: phone,
        code: code
      }
    });

    if (response.data.code === 0) {
      wx.setStorageSync('user', response.data.data);
      wx.showToast({
        title: '绑定成功',
        icon: 'success'
      });
      
      // 绑定成功后跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
    } else {
      throw new Error(response.data.message);
    }

  } catch (error) {
    console.error('绑定失败:', error);
    wx.showToast({
      title: error.message || '绑定失败',
      icon: 'error'
    });
  }
};
```

## 3. 部署配置

### 3.1 环境变量配置

```env
# 微信小程序配置
WECHAT_MINI_PROGRAM_APPID=your_miniprogram_appid
WECHAT_MINI_PROGRAM_SECRET=your_miniprogram_secret
WECHAT_MINI_PROGRAM_TOKEN=your_miniprogram_token
WECHAT_MINI_PROGRAM_AES_KEY=your_miniprogram_aes_key

# 微信开放平台配置（用于获取unionid）
WECHAT_OPEN_PLATFORM_APPID=your_open_platform_appid
WECHAT_OPEN_PLATFORM_SECRET=your_open_platform_secret
```

### 3.2 数据库迁移

```bash
# 执行数据库迁移
php artisan migrate

# 或者直接执行SQL
mysql -u username -p database_name < migration.sql
```

## 4. 测试方案

### 4.1 单元测试

```php
// tests/Feature/MiniprogramLoginTest.php
class MiniprogramLoginTest extends TestCase
{
    public function test_miniprogram_login_success()
    {
        // 模拟微信API响应
        $this->mockWechatApi();
        
        $response = $this->postJson('/api/user/miniprogram-login', [
            'code' => 'test_code'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'code',
                'message',
                'data' => [
                    'access_token',
                    'user' => [
                        'id',
                        'openid',
                        'platform_type'
                    ]
                ]
            ]);
    }

    public function test_user_merge_by_phone()
    {
        // 创建测试用户
        $existingUser = User::factory()->create(['phone' => '13800138000']);
        $wechatUser = User::factory()->create(['openid' => 'test_openid']);

        // 测试绑定手机号触发合并
        $response = $this->actingAs($wechatUser, 'user')
            ->postJson('/api/user/bind-phone', [
                'phone' => '13800138000',
                'code' => '123456'
            ]);

        $response->assertStatus(200);
        
        // 验证用户已合并
        $this->assertDatabaseMissing('users', ['id' => $wechatUser->id]);
        $this->assertDatabaseHas('users', [
            'id' => $existingUser->id,
            'openid' => 'test_openid'
        ]);
    }
}
```

### 4.2 集成测试

测试场景：
1. 新用户小程序登录
2. 已有用户小程序登录
3. 手机号绑定和用户合并
4. 跨平台用户统一（基于unionid）

## 5. 安全考虑

### 5.1 数据安全
- 敏感数据加密存储
- session_key定期更新
- 用户合并操作记录审计日志

### 5.2 接口安全
- 验证微信返回的signature
- 防止重放攻击
- 限制接口调用频率

### 5.3 隐私保护
- 遵循用户隐私协议
- 手机号等敏感信息脱敏
- 支持用户数据删除

## 6. 监控与运维

### 6.1 日志记录
- 登录成功/失败日志
- 用户合并操作日志
- 微信API调用日志
- 异常错误日志

### 6.2 性能监控
- 接口响应时间
- 微信API调用耗时
- 数据库查询性能
- 用户合并操作耗时

### 6.3 业务监控
- 新用户注册量
- 用户合并成功率
- 登录成功率
- 平台用户分布

## 7. 兼容性保证

### 7.1 现有API保持不变
- 所有现有接口保持向后兼容
- 响应格式不变
- 业务逻辑不受影响

### 7.2 数据库兼容
- 新增字段设置为可空
- 现有数据不受影响
- 支持渐进式迁移

### 7.3 客户端兼容
- H5端登录流程不变
- 现有用户无需重新登录
- 支持多端并存

## 8. 实施计划

### 8.1 第一阶段：基础功能
- 数据库结构调整
- 小程序登录接口开发
- 基础测试验证

### 8.2 第二阶段：用户合并
- 手机号绑定功能
- 用户合并逻辑
- 数据迁移功能

### 8.3 第三阶段：优化完善
- 性能优化
- 监控完善
- 文档完善

## 9. 风险评估与应对

### 9.1 技术风险
- **风险**：用户数据合并失败
- **应对**：完善事务处理，添加回滚机制

### 9.2 业务风险
- **风险**：误合并用户账号
- **应对**：增加用户确认流程，提供申诉机制

### 9.3 运维风险
- **风险**：微信API调用失败
- **应对**：增加重试机制，降级处理

## 10. 总结

本方案通过扩展现有用户认证系统，实现了微信小程序登录的完整支持，并提供了基于手机号的用户合并机制。方案设计充分考虑了向后兼容性、数据安全性和用户体验，能够满足多平台用户统一管理的需求。

实施本方案后，系统将具备：
- 完整的微信小程序登录功能
- 智能的用户账号合并机制
- 良好的跨平台用户体验
- 完善的安全保障措施
- 可靠的监控运维能力

---

**文档版本**: v1.0  
**创建日期**: 2024-12-17  
**维护团队**: 开发团队 
