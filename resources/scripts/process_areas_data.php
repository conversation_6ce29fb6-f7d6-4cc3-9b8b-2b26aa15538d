<?php
/**
 * 中国省市区数据处理脚本
 *
 * 此脚本用于处理不同格式的中国省市区JSON数据，
 * 将其转换为标准格式，以便导入到数据库中。
 *
 * 使用方法：
 * php process_areas_data.php input.json output.json
 */

// 检查命令行参数
if ($argc < 3) {
    echo "用法: php process_areas_data.php 输入JSON文件 输出JSON文件\n";
    exit(1);
}

$inputFile = $argv[1];
$outputFile = $argv[2];

// 检查输入文件是否存在
if (!file_exists($inputFile)) {
    echo "错误: 输入文件 '{$inputFile}' 不存在\n";
    exit(1);
}

// 读取输入JSON
$jsonContent = file_get_contents($inputFile);
$data = json_decode($jsonContent, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "JSON解析错误: " . json_last_error_msg() . "\n";
    exit(1);
}

// 分析JSON结构
$standardizedData = [];

// 检测JSON结构类型并处理
if (isArrayOfProvinces($data)) {
    // 已经是标准结构: [省份, 省份, ...]
    $standardizedData = processStandardFormat($data);
} elseif (isNestedObject($data)) {
    // 嵌套对象结构: {省份代码: {name: "省份名", cities: {...}}, ...}
    $standardizedData = processNestedObjectFormat($data);
} elseif (isFlatArray($data)) {
    // 扁平数组结构: [{id: "省份代码", name: "省份名", pid: 0}, ...]
    $standardizedData = processFlatArrayFormat($data);
} else {
    echo "错误: 无法识别的JSON数据结构\n";
    exit(1);
}

// 写入输出文件
file_put_contents($outputFile, json_encode($standardizedData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
echo "处理完成! 数据已保存到: {$outputFile}\n";
exit(0);

/**
 * 检查是否为标准省份数组格式
 */
function isArrayOfProvinces(array $data): bool
{
    if (!is_array($data) || empty($data)) {
        return false;
    }

    $firstItem = reset($data);
    return is_array($firstItem) &&
           isset($firstItem['name']) &&
           isset($firstItem['code']) &&
           (isset($firstItem['children']) || isset($firstItem['cities']));
}

/**
 * 检查是否为嵌套对象格式
 */
function isNestedObject(array $data): bool
{
    if (!is_array($data) || empty($data)) {
        return false;
    }

    $firstKey = array_key_first($data);
    $firstItem = $data[$firstKey];

    return is_array($firstItem) &&
           isset($firstItem['name']) &&
           (isset($firstItem['cities']) || isset($firstItem['districts']));
}

/**
 * 检查是否为扁平数组格式
 */
function isFlatArray(array $data): bool
{
    if (!is_array($data) || empty($data)) {
        return false;
    }

    $firstItem = reset($data);
    return is_array($firstItem) &&
           isset($firstItem['name']) &&
           (isset($firstItem['id']) || isset($firstItem['code'])) &&
           isset($firstItem['pid']);
}

/**
 * 处理标准格式数据
 */
function processStandardFormat(array $data): array
{
    $result = [];

    foreach ($data as $province) {
        $standardProvince = [
            'name' => $province['name'],
            'code' => $province['code'] ?? $province['id'] ?? '',
            'children' => []
        ];

        $cities = $province['children'] ?? $province['cities'] ?? [];

        foreach ($cities as $city) {
            $standardCity = [
                'name' => $city['name'],
                'code' => $city['code'] ?? $city['id'] ?? '',
                'children' => []
            ];

            $districts = $city['children'] ?? $city['districts'] ?? [];

            foreach ($districts as $district) {
                $standardDistrict = [
                    'name' => $district['name'],
                    'code' => $district['code'] ?? $district['id'] ?? ''
                ];

                $standardCity['children'][] = $standardDistrict;
            }

            $standardProvince['children'][] = $standardCity;
        }

        $result[] = $standardProvince;
    }

    return $result;
}

/**
 * 处理嵌套对象格式数据
 */
function processNestedObjectFormat(array $data): array
{
    $result = [];

    foreach ($data as $provinceCode => $province) {
        $standardProvince = [
            'name' => $province['name'],
            'code' => $provinceCode,
            'children' => []
        ];

        $cities = $province['cities'] ?? [];

        foreach ($cities as $cityCode => $city) {
            $standardCity = [
                'name' => $city['name'],
                'code' => $cityCode,
                'children' => []
            ];

            $districts = $city['districts'] ?? [];

            foreach ($districts as $districtCode => $districtName) {
                $standardDistrict = [
                    'name' => is_array($districtName) ? $districtName['name'] : $districtName,
                    'code' => $districtCode
                ];

                $standardCity['children'][] = $standardDistrict;
            }

            $standardProvince['children'][] = $standardCity;
        }

        $result[] = $standardProvince;
    }

    return $result;
}

/**
 * 处理扁平数组格式数据
 */
function processFlatArrayFormat(array $data): array
{
    $result = [];
    $provinces = [];
    $cities = [];
    $districts = [];

    // 按层级分类
    foreach ($data as $item) {
        $pid = $item['pid'] ?? $item['parentId'] ?? 0;

        if ($pid == 0) {
            $provinces[$item['id'] ?? $item['code']] = $item;
        } elseif (isset($provinces[$pid])) {
            $cities[$item['id'] ?? $item['code']] = $item;
        } else {
            $districts[$item['id'] ?? $item['code']] = $item;
        }
    }

    // 构建层级结构
    foreach ($provinces as $provinceId => $province) {
        $standardProvince = [
            'name' => $province['name'],
            'code' => $provinceId,
            'children' => []
        ];

        foreach ($cities as $cityId => $city) {
            if (($city['pid'] ?? $city['parentId'] ?? 0) == $provinceId) {
                $standardCity = [
                    'name' => $city['name'],
                    'code' => $cityId,
                    'children' => []
                ];

                foreach ($districts as $districtId => $district) {
                    if (($district['pid'] ?? $district['parentId'] ?? 0) == $cityId) {
                        $standardDistrict = [
                            'name' => $district['name'],
                            'code' => $districtId
                        ];

                        $standardCity['children'][] = $standardDistrict;
                    }
                }

                $standardProvince['children'][] = $standardCity;
            }
        }

        $result[] = $standardProvince;
    }

    return $result;
}
