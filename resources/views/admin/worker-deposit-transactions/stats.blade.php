@extends('admin::layouts.content')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">师傅押金流水统计</h3>
                    <div class="card-tools">
                        <a href="{{ admin_url('worker-deposit-transactions') }}" class="btn btn-sm btn-primary">
                            <i class="fa fa-list"></i> 返回流水列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 总体统计 -->
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ $totalTransactions }}</h3>
                                    <p>总流水笔数</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-list"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>￥{{ number_format($totalAmount, 2) }}</h3>
                                    <p>总流水金额</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-money"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>￥{{ number_format($depositAmount, 2) }}</h3>
                                    <p>缴纳押金总额</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-plus"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3>￥{{ number_format($deductionAmount, 2) }}</h3>
                                    <p>扣除押金总额</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-minus"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类统计 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">押金操作类型统计</h3>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>操作类型</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge badge-success">缴纳押金</span></td>
                                                <td>￥{{ number_format($depositAmount, 2) }}</td>
                                                <td>{{ $totalAmount > 0 ? number_format(($depositAmount / $totalAmount) * 100, 2) : 0 }}%</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-primary">返还押金</span></td>
                                                <td>￥{{ number_format($returnAmount, 2) }}</td>
                                                <td>{{ $totalAmount > 0 ? number_format(($returnAmount / $totalAmount) * 100, 2) : 0 }}%</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-danger">扣除押金</span></td>
                                                <td>￥{{ number_format($deductionAmount, 2) }}</td>
                                                <td>{{ $totalAmount > 0 ? number_format(($deductionAmount / $totalAmount) * 100, 2) : 0 }}%</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-warning">退回押金</span></td>
                                                <td>￥{{ number_format($refundAmount, 2) }}</td>
                                                <td>{{ $totalAmount > 0 ? number_format(($refundAmount / $totalAmount) * 100, 2) : 0 }}%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">押金净流入统计</h3>
                                </div>
                                <div class="card-body">
                                    @php
                                        $netInflow = $depositAmount - $returnAmount - $deductionAmount - $refundAmount;
                                    @endphp
                                    <div class="info-box">
                                        <span class="info-box-icon {{ $netInflow >= 0 ? 'bg-success' : 'bg-danger' }}">
                                            <i class="fa {{ $netInflow >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }}"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">押金净流入</span>
                                            <span class="info-box-number">￥{{ number_format(abs($netInflow), 2) }}</span>
                                            <span class="info-box-more">
                                                {{ $netInflow >= 0 ? '净流入' : '净流出' }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>计算说明：</strong><br>
                                            净流入 = 缴纳押金 - 返还押金 - 扣除押金 - 退回押金<br>
                                            = {{ number_format($depositAmount, 2) }} - {{ number_format($returnAmount, 2) }} - {{ number_format($deductionAmount, 2) }} - {{ number_format($refundAmount, 2) }}<br>
                                            = {{ number_format($netInflow, 2) }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 月度统计 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">近12个月押金流水统计</h3>
                                </div>
                                <div class="card-body">
                                    @if($monthlyStats->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>月份</th>
                                                        <th>缴纳押金</th>
                                                        <th>返还押金</th>
                                                        <th>扣除押金</th>
                                                        <th>退回押金</th>
                                                        <th>月度净流入</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($monthlyStats as $month => $stats)
                                                        @php
                                                            $deposit = $stats->where('type', 'deposit')->first();
                                                            $return = $stats->where('type', 'return')->first();
                                                            $deduction = $stats->where('type', 'deduction')->first();
                                                            $refund = $stats->where('type', 'refund')->first();

                                                            $depositAmount = $deposit ? $deposit->total_amount : 0;
                                                            $returnAmount = $return ? $return->total_amount : 0;
                                                            $deductionAmount = $deduction ? $deduction->total_amount : 0;
                                                            $refundAmount = $refund ? $refund->total_amount : 0;

                                                            $monthlyNet = $depositAmount - $returnAmount - $deductionAmount - $refundAmount;
                                                        @endphp
                                                        <tr>
                                                            <td><strong>{{ $month }}</strong></td>
                                                            <td class="text-success">
                                                                ￥{{ number_format($depositAmount, 2) }}
                                                                @if($deposit)
                                                                    <small class="text-muted">({{ $deposit->count }}笔)</small>
                                                                @endif
                                                            </td>
                                                            <td class="text-primary">
                                                                ￥{{ number_format($returnAmount, 2) }}
                                                                @if($return)
                                                                    <small class="text-muted">({{ $return->count }}笔)</small>
                                                                @endif
                                                            </td>
                                                            <td class="text-danger">
                                                                ￥{{ number_format($deductionAmount, 2) }}
                                                                @if($deduction)
                                                                    <small class="text-muted">({{ $deduction->count }}笔)</small>
                                                                @endif
                                                            </td>
                                                            <td class="text-warning">
                                                                ￥{{ number_format($refundAmount, 2) }}
                                                                @if($refund)
                                                                    <small class="text-muted">({{ $refund->count }}笔)</small>
                                                                @endif
                                                            </td>
                                                            <td class="{{ $monthlyNet >= 0 ? 'text-success' : 'text-danger' }}">
                                                                <strong>{{ $monthlyNet >= 0 ? '+' : '' }}￥{{ number_format($monthlyNet, 2) }}</strong>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="text-center text-muted py-4">
                                            <i class="fa fa-info-circle fa-2x mb-2"></i>
                                            <p>暂无月度统计数据</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(function() {
    // 可以在这里添加图表等交互功能
    console.log('押金流水统计页面加载完成');
});
</script>
@endsection
