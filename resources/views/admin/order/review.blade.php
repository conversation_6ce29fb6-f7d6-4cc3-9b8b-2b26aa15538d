@php
/** @var \App\Models\OrderReview $review */
$review = $review ?? null;
@endphp

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">用户评价</h5>
    </div>
    <div class="card-body">
        @if(!$review)
            <div class="empty-box">
                <p class="text-center text-muted">暂无用户评价</p>
            </div>
        @else
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="font-weight-bold mr-3">服务质量评分：</div>
                        <div class="rating-stars">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= $review->service_rating)
                                    <i class="fa fa-star text-warning"></i>
                                @else
                                    <i class="fa fa-star-o text-muted"></i>
                                @endif
                            @endfor
                            <span class="ml-2">{{ $review->service_rating }}/5</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="font-weight-bold mr-3">师傅评分：</div>
                        <div class="rating-stars">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= $review->worker_rating)
                                    <i class="fa fa-star text-warning"></i>
                                @else
                                    <i class="fa fa-star-o text-muted"></i>
                                @endif
                            @endfor
                            <span class="ml-2">{{ $review->worker_rating }}/5</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="font-weight-bold">评价内容：</label>
                <div class="p-3 bg-light rounded">
                    {{ $review->content ?: '用户未填写评价内容' }}
                </div>
            </div>

            @if($review->images && is_array($review->images) && count($review->images) > 0)
                <div class="form-group">
                    <label class="font-weight-bold">评价图片：</label>
                    <div class="row">
                        @foreach($review->images as $image)
                            <div class="col-md-3 col-sm-4 col-6 mb-3">
                                <a href="{{ $image }}" target="_blank" class="d-block">
                                    <img src="{{ $image }}" class="img-fluid rounded" alt="评价图片">
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <div class="form-group mb-0">
                <label class="font-weight-bold">评价时间：</label>
                <div>{{ $review->created_at }}</div>
            </div>
        @endif
    </div>
</div>
