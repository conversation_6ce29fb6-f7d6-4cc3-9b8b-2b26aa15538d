@php
/** @var \Illuminate\Support\Collection $materialOrders */
$materialOrders = $materialOrders ?? collect();

// 确保 $materialOrders 是一个集合而不是闭包
if (is_callable($materialOrders)) {
    $materialOrders = $materialOrders($model ?? null) ?? collect();
} elseif (!($materialOrders instanceof \Illuminate\Support\Collection)) {
    $materialOrders = collect();
}
@endphp

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fa fa-shopping-bag"></i> 关联材料订单
        </h5>
        <span class="badge badge-primary badge-lg">{{ $materialOrders->count() }}个</span>
    </div>
    <div class="card-body">
        @if($materialOrders->isEmpty())
            <div class="text-center text-muted py-4">
                <i class="fa fa-shopping-cart fa-2x mb-2"></i>
                <p>暂无关联的材料订单</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th style="font-weight: bold; color: #495057;">
                                <i class="fa fa-barcode"></i> 材料订单号
                            </th>
                            <th style="font-weight: bold; color: #495057;">
                                <i class="fa fa-cube"></i> 材料名称
                            </th>
                            <th style="width: 80px; font-weight: bold; color: #495057;">
                                <i class="fa fa-sort-numeric-asc"></i> 数量
                            </th>
                            <th style="width: 100px; font-weight: bold; color: #495057;">
                                <i class="fa fa-tag"></i> 单价
                            </th>
                            <th style="width: 100px; font-weight: bold; color: #495057;">
                                <i class="fa fa-calculator"></i> 总价
                            </th>
                            <th style="width: 100px; font-weight: bold; color: #495057;">
                                <i class="fa fa-info-circle"></i> 状态
                            </th>
                            <th style="width: 150px; font-weight: bold; color: #495057;">
                                <i class="fa fa-calendar"></i> 创建时间
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($materialOrders as $materialOrder)
                            @if($materialOrder->items && $materialOrder->items->count() > 0)
                                @foreach($materialOrder->items as $item)
                                <tr>
                                    <td style="font-weight: 500; color: #212529;">
                                        <code>{{ $materialOrder->material_order_no ?? 'N/A' }}</code>
                                    </td>
                                    <td style="font-weight: 500; color: #212529;">
                                        {{ $item->material->name ?? '未知材料' }}
                                    </td>
                                    <td class="text-center">
                                        {{ $item->quantity ?? 0 }}
                                    </td>
                                    <td class="text-right" style="font-weight: 500; color: #28a745;">
                                        ￥{{ number_format($item->price ?? 0, 2) }}
                                    </td>
                                    <td class="text-right" style="font-weight: bold; color: #dc3545;">
                                        ￥{{ number_format($item->amount ?? 0, 2) }}
                                    </td>
                                    <td class="text-center">
                                        @switch($materialOrder->status)
                                            @case(0)
                                                <span class="badge badge-warning">
                                                    <i class="fa fa-clock-o"></i> 待付款
                                                </span>
                                                @break
                                            @case(1)
                                                <span class="badge badge-success">
                                                    <i class="fa fa-check"></i> 已付款
                                                </span>
                                                @break
                                            @case(2)
                                                <span class="badge badge-danger">
                                                    <i class="fa fa-times"></i> 已取消
                                                </span>
                                                @break
                                            @case(3)
                                                <span class="badge badge-info">
                                                    <i class="fa fa-upload"></i> 待上传使用图片
                                                </span>
                                                @break
                                            @case(4)
                                                <span class="badge badge-warning">
                                                    <i class="fa fa-clock-o"></i> 待客户确认
                                                </span>
                                                @break
                                            @case(5)
                                                <span class="badge badge-success">
                                                    <i class="fa fa-check-circle"></i> 已完成
                                                </span>
                                                @break
                                            @default
                                                <span class="badge badge-secondary">
                                                    <i class="fa fa-question"></i> 未知
                                                </span>
                                        @endswitch
                                    </td>
                                    <td style="font-size: 12px; color: #6c757d;">
                                        {{ $materialOrder->created_at->format('Y-m-d H:i') }}
                                    </td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td style="font-weight: 500; color: #212529;">
                                        <code>{{ $materialOrder->material_order_no ?? 'N/A' }}</code>
                                    </td>
                                    <td colspan="4" class="text-center text-muted">
                                        <i class="fa fa-exclamation-triangle"></i> 该材料订单暂无明细项目
                                    </td>
                                    <td class="text-center">
                                        @switch($materialOrder->status)
                                            @case(0)
                                                <span class="badge badge-warning">
                                                    <i class="fa fa-clock-o"></i> 待付款
                                                </span>
                                                @break
                                            @case(1)
                                                <span class="badge badge-success">
                                                    <i class="fa fa-check"></i> 已付款
                                                </span>
                                                @break
                                            @case(2)
                                                <span class="badge badge-danger">
                                                    <i class="fa fa-times"></i> 已取消
                                                </span>
                                                @break
                                            @case(3)
                                                <span class="badge badge-info">
                                                    <i class="fa fa-upload"></i> 待上传使用图片
                                                </span>
                                                @break
                                            @case(4)
                                                <span class="badge badge-warning">
                                                    <i class="fa fa-clock-o"></i> 待客户确认
                                                </span>
                                                @break
                                            @case(5)
                                                <span class="badge badge-success">
                                                    <i class="fa fa-check-circle"></i> 已完成
                                                </span>
                                                @break
                                            @default
                                                <span class="badge badge-secondary">
                                                    <i class="fa fa-question"></i> 未知
                                                </span>
                                        @endswitch
                                    </td>
                                    <td style="font-size: 12px; color: #6c757d;">
                                        {{ $materialOrder->created_at->format('Y-m-d H:i') }}
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- 统计信息 -->
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <strong>统计信息：</strong>
                        材料订单数：<span class="badge badge-primary">{{ $materialOrders->count() }}</span>
                        材料项目数：<span class="badge badge-info">{{ $materialOrders->sum(function($order) { return $order->items->count(); }) }}</span>
                        总金额：<span class="badge badge-success">￥{{ number_format($materialOrders->sum('total_amount'), 2) }}</span>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<style>
.table th {
    border-top: none;
    background-color: #f8f9fa;
}
.table td {
    vertical-align: middle;
    padding: 12px 8px;
}
.badge-lg {
    font-size: 14px;
    padding: 6px 12px;
}
code {
    color: #e83e8c;
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
