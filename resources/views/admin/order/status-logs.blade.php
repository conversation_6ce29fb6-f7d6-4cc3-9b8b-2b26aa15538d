@php
/** @var \Illuminate\Support\Collection|\Closure $logs */
// 如果logs是闭包，则执行它
if ($logs instanceof \Closure) {
    $logs = $logs($model ?? null);
} else {
    $logs = $logs ?? collect();
}
@endphp

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fa fa-history"></i> 订单状态变更历史
        </h5>
    </div>
    <div class="card-body">
        @if($logs->isEmpty())
            <div class="text-center text-muted py-4">
                <i class="fa fa-info-circle fa-2x mb-2"></i>
                <p>暂无状态变更记录</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 180px; font-weight: bold; color: #495057;">
                                <i class="fa fa-clock-o"></i> 时间
                            </th>
                            <th style="font-weight: bold; color: #495057;">
                                <i class="fa fa-exchange"></i> 状态变更
                            </th>
                            <th style="width: 150px; font-weight: bold; color: #495057;">
                                <i class="fa fa-user"></i> 操作人
                            </th>
                            <th style="font-weight: bold; color: #495057;">
                                <i class="fa fa-comment"></i> 备注
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($logs as $log)
                            <tr>
                                <td style="font-weight: 500; color: #212529;">
                                        {{ $log->created_at }}
                                </td>
                                <td>
                                    <span class="badge badge-secondary" style="font-size: 12px;">
                                        {{ $log->getStatusText($log->previous_status) }}
                                    </span>
                                    <i class="fa fa-arrow-right mx-2" style="color: #007bff;"></i>
                                    <span class="badge badge-primary" style="font-size: 12px;">
                                        {{ $log->getStatusText($log->current_status) }}
                                    </span>
                                </td>
                                <td style="font-weight: 500; color: #212529;">
                                    @if($log->operator_type === 'admin')
                                        <span class="badge badge-danger">
                                            <i class="fa fa-user-shield"></i> 管理员
                                        </span>
                                    @elseif($log->operator_type === 'user')
                                        <span class="badge badge-info">
                                            <i class="fa fa-user"></i> 用户
                                        </span>
                                    @else
                                        <span class="badge badge-success">
                                            <i class="fa fa-wrench"></i> 师傅
                                        </span>
                                    @endif
                                    <br>
                                    <small class="text-muted">(ID: {{ $log->operator_id }})</small>
                                </td>
                                <td style="color: #495057;">
                                    @if($log->remark)
                                        <div class="alert alert-light mb-0 py-2" style="font-size: 13px;">
                                            {{ $log->remark }}
                                        </div>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>

<style>
.table th {
    border-top: none;
    background-color: #f8f9fa;
}
.table td {
    vertical-align: middle;
    padding: 12px 8px;
}
.badge {
    font-size: 11px;
    padding: 4px 8px;
}
</style>

<script>
function getStatusText(status) {
    const statusMap = {
        '0': '待付款',
        '1': '待接单',
        '2': '待师傅上门',
        '3': '待设定服务费用',
        '4': '待维修',
        '5': '待维修确认',
        '6': '已完成',
        '7': '已取消'
    };
    return statusMap[status] || '未知状态';
}

function logStatusColor(status) {
    const colorMap = {
        '0': 'default',
        '1': 'primary',
        '2': 'info',
        '3': 'warning',
        '4': 'warning',
        '5': 'warning',
        '6': 'success',
        '7': 'danger'
    };
    return colorMap[status] || 'default';
}

// 将函数绑定到window对象，供视图使用
window.$getStatusText = getStatusText;
window.$logStatusColor = logStatusColor;
</script>
