<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('course_sections', function (Blueprint $table) {
            // 删除旧的外键和字段
            $table->dropForeign(['main_course_id']);
            $table->dropColumn('main_course_id');
            $table->dropColumn('name');

            // 添加新的字段
            $table->unsignedBigInteger('course_id')->after('id');
            $table->string('title')->after('course_id');
            $table->text('description')->nullable()->after('title');
            $table->enum('type', ['video', 'document'])->default('video')->after('description');
            $table->integer('duration')->default(0)->after('type');
            $table->integer('file_size')->default(0)->after('duration');
            $table->string('file_url')->nullable()->after('file_size');
            $table->boolean('status')->default(true)->after('sort');

            // 添加外键约束
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('course_sections', function (Blueprint $table) {
            // 删除新添加的字段和外键
            $table->dropForeign(['course_id']);
            $table->dropColumn('course_id');
            $table->dropColumn('title');
            $table->dropColumn('description');
            $table->dropColumn('type');
            $table->dropColumn('duration');
            $table->dropColumn('file_size');
            $table->dropColumn('file_url');
            $table->dropColumn('status');

            // 恢复旧的字段
            $table->unsignedBigInteger('main_course_id')->nullable();
            $table->string('name');

            // 恢复外键约束
            $table->foreign('main_course_id')->references('id')->on('main_courses');
        });
    }
};
