<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * 注意：此迁移已不再需要，改用category_skills表实现多对多关系
     */
    public function up(): void
    {
        // 不再添加skill_id字段，改用category_skills表实现多对多关系
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 不需要执行任何操作
    }
};
