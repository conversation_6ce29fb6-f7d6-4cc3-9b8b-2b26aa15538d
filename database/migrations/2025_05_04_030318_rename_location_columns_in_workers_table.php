<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workers', function (Blueprint $table) {
            // 先删除已有的字段，如果存在
            if (Schema::hasColumn('workers', 'province')) {
                $table->dropColumn(['province', 'city', 'district']);
            }

            // 添加新的ID字段
            $table->unsignedBigInteger('province_id')->nullable()->after('id_card')->comment('省份ID');
            $table->unsignedBigInteger('city_id')->nullable()->after('province_id')->comment('城市ID');
            $table->unsignedBigInteger('district_id')->nullable()->after('city_id')->comment('区县ID');

            // 添加外键约束
            $table->foreign('province_id')->references('id')->on('areas')->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('areas')->onDelete('set null');
            $table->foreign('district_id')->references('id')->on('areas')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workers', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['province_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['district_id']);

            // 删除ID字段
            $table->dropColumn(['province_id', 'city_id', 'district_id']);

            // 恢复原来的字段
            $table->string('province')->nullable()->after('id_card')->comment('省份');
            $table->string('city')->nullable()->after('province')->comment('城市');
            $table->string('district')->nullable()->after('city')->comment('区县');
        });
    }
};
