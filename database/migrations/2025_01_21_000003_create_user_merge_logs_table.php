<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_merge_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('main_user_id')->comment('主用户ID（保留的账号）');
            $table->unsignedBigInteger('merged_user_id')->comment('被合并用户ID');
            $table->enum('merge_type', ['phone_to_wechat', 'wechat_to_phone', 'duplicate_merge'])->comment('合并类型');
            $table->json('merge_data')->nullable()->comment('合并的数据详情');
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending')->comment('合并状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->string('operator_type', 20)->default('system')->comment('操作者类型：user/admin/system');
            $table->unsignedBigInteger('operator_id')->nullable()->comment('操作者ID');
            $table->timestamp('started_at')->useCurrent()->comment('开始时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamps();

            // 索引
            $table->index('main_user_id');
            $table->index('merged_user_id');
            $table->index(['status', 'created_at']);
            $table->index('merge_type');

            // 外键约束
            $table->foreign('main_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('merged_user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_merge_logs');
    }
};
