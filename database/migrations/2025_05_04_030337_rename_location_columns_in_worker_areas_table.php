<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('worker_areas', function (Blueprint $table) {
            // 先删除已有的字段，如果存在
            if (Schema::hasColumn('worker_areas', 'province')) {
                $table->dropColumn(['province', 'city', 'district']);
            }

            // 添加新的ID字段，如果不存在
            if (!Schema::hasColumn('worker_areas', 'province_id')) {
                $table->unsignedBigInteger('province_id')->nullable()->comment('省份ID');
                $table->unsignedBigInteger('city_id')->nullable()->comment('城市ID');
                $table->unsignedBigInteger('district_id')->nullable()->comment('区县ID');

                // 添加外键约束
                $table->foreign('province_id')->references('id')->on('areas')->onDelete('set null');
                $table->foreign('city_id')->references('id')->on('areas')->onDelete('set null');
                $table->foreign('district_id')->references('id')->on('areas')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('worker_areas', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['province_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['district_id']);

            // 删除ID字段
            $table->dropColumn(['province_id', 'city_id', 'district_id']);

            // 恢复原来的字段
            $table->string('province')->nullable()->comment('省份');
            $table->string('city')->nullable()->comment('城市');
            $table->string('district')->nullable()->comment('区县');
        });
    }
};
