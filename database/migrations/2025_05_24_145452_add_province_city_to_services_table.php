<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->bigInteger('province_id')->unsigned()->nullable()->after('area_id')->comment('省份ID');
            $table->bigInteger('city_id')->unsigned()->nullable()->after('province_id')->comment('城市ID');

            // 添加索引
            $table->index('province_id');
            $table->index('city_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex(['province_id']);
            $table->dropIndex(['city_id']);
            $table->dropColumn(['province_id', 'city_id']);
        });
    }
};
