<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workers', function (Blueprint $table) {
            $table->string('province')->nullable()->after('id_card')->comment('省份');
            $table->string('city')->nullable()->after('province')->comment('城市');
            $table->string('district')->nullable()->after('city')->comment('区县');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workers', function (Blueprint $table) {
            $table->dropColumn(['province', 'city', 'district']);
        });
    }
};
