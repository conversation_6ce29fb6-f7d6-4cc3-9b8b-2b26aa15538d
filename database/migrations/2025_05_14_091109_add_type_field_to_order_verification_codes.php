<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_verification_codes', function (Blueprint $table) {
            // 添加type字段，默认为booking类型
            $table->string('type', 20)->default('booking')->after('code')->comment('核销码类型:booking=预约核销码,service=维修核销码');
        });

        // 为已存在的记录设置默认值
        DB::table('order_verification_codes')->update(['type' => 'booking']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_verification_codes', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
