<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_params', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('参数名称');
            $table->string('key', 100)->unique()->comment('参数键名');
            $table->text('value')->nullable()->comment('参数值');
            $table->string('group', 50)->default('basic')->comment('参数分组');
            $table->string('type', 30)->default('text')->comment('参数类型');
            $table->text('options')->nullable()->comment('选项（JSON格式）');
            $table->string('remark', 255)->nullable()->comment('参数备注');
            $table->integer('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态：0禁用，1启用');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_params');
    }
};
