<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('category_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_category_id')->constrained('service_categories')->onDelete('cascade')->comment('服务分类ID');
            $table->foreignId('skill_id')->constrained('skills')->onDelete('cascade')->comment('技能ID');
            $table->timestamps();

            // 创建唯一索引，确保一个分类不会重复关联同一个技能
            $table->unique(['service_category_id', 'skill_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('category_skills');
    }
};
