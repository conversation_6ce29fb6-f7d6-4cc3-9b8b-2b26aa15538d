<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('worker_bank_cards', function (Blueprint $table) {
            $table->unsignedBigInteger('bank_id')->nullable()->after('worker_id')->comment('银行ID');
            $table->foreign('bank_id')->references('id')->on('banks')->onDelete('set null');
            $table->index('bank_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('worker_bank_cards', function (Blueprint $table) {
            $table->dropForeign(['bank_id']);
            $table->dropIndex(['bank_id']);
            $table->dropColumn('bank_id');
        });
    }
};
