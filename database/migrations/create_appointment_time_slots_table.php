<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointment_time_slots', function (Blueprint $table) {
            $table->id();
            $table->string('day_name', 20)->comment('星期名称');
            $table->tinyInteger('day_of_week')->comment('星期几（1-7，1代表周一）');
            $table->string('time_period', 20)->comment('时间段（上午、下午、晚上）');
            $table->string('start_time', 5)->comment('开始时间（格式：HH:MM）');
            $table->string('end_time', 5)->comment('结束时间（格式：HH:MM）');
            $table->tinyInteger('status')->default(1)->comment('状态（0禁用, 1启用）');
            $table->integer('sort')->default(0)->comment('排序（数字越大越靠前）');
            $table->timestamps();

            // 添加索引
            $table->index('day_of_week');
            $table->index('status');
            $table->index('sort');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_time_slots');
    }
};
