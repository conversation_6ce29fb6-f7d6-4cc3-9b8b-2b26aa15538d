<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // 删除不需要的字段
            $table->dropForeign(['main_course_id']);
            $table->dropForeign(['section_id']);
            $table->dropForeign(['exam_subject_id']);

            $table->dropColumn('main_course_id');
            $table->dropColumn('section_id');
            $table->dropColumn('exam_subject_id');

            // 添加新字段
            $table->boolean('status')->default(true)->after('views');
            $table->integer('sort')->default(0)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // 恢复删除的字段
            $table->unsignedBigInteger('main_course_id')->nullable();
            $table->unsignedBigInteger('section_id')->nullable();
            $table->unsignedBigInteger('exam_subject_id')->nullable();

            $table->foreign('main_course_id')->references('id')->on('main_courses');
            $table->foreign('section_id')->references('id')->on('course_sections');
            $table->foreign('exam_subject_id')->references('id')->on('exam_subjects');

            // 删除新添加的字段
            $table->dropColumn('status');
            $table->dropColumn('sort');
        });
    }
};
