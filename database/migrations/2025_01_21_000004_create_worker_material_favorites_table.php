<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('worker_material_favorites', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('worker_id')->comment('师傅ID');
            $table->unsignedBigInteger('material_id')->comment('材料ID');
            $table->timestamps();

            // 设置索引
            $table->index('worker_id');
            $table->index('material_id');

            // 设置唯一索引，防止重复收藏
            $table->unique(['worker_id', 'material_id'], 'worker_material_unique');

            // 设置外键约束
            $table->foreign('worker_id')->references('id')->on('workers')->onDelete('cascade');
            $table->foreign('material_id')->references('id')->on('materials')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('worker_material_favorites');
    }
};
