<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 为服务添加推荐字段
        Schema::table('services', function (Blueprint $table) {
            $table->tinyInteger('is_recommended')->default(0)->comment('是否推荐 0不推荐 1推荐');
        });

        // 为课程添加推荐字段
        Schema::table('courses', function (Blueprint $table) {
            $table->tinyInteger('is_recommended')->default(0)->comment('是否推荐 0不推荐 1推荐');
        });

        // 为工作添加推荐字段
        Schema::table('jobs', function (Blueprint $table) {
            $table->tinyInteger('is_recommended')->default(0)->comment('是否推荐 0不推荐 1推荐');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除服务的推荐字段
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('is_recommended');
        });

        // 删除课程的推荐字段
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('is_recommended');
        });

        // 删除工作的推荐字段
        Schema::table('jobs', function (Blueprint $table) {
            $table->dropColumn('is_recommended');
        });
    }
};
