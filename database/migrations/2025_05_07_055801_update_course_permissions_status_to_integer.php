<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\CoursePermission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 先将现有的字符串状态转换为整数
        DB::table('course_permissions')
            ->where('status', '待审核')
            ->update(['status' => CoursePermission::STATUS_PENDING]);

        DB::table('course_permissions')
            ->where('status', '已通过')
            ->update(['status' => CoursePermission::STATUS_APPROVED]);

        DB::table('course_permissions')
            ->where('status', '已拒绝')
            ->update(['status' => CoursePermission::STATUS_REJECTED]);

        Schema::table('course_permissions', function (Blueprint $table) {
            // 修改status字段类型，从string或varchar改为tinyint
            $table->tinyInteger('status')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('course_permissions', function (Blueprint $table) {
            // 恢复字段类型为字符串
            $table->string('status')->change();
        });

        // 恢复数据为原始字符串状态
        DB::table('course_permissions')
            ->where('status', CoursePermission::STATUS_PENDING)
            ->update(['status' => '待审核']);

        DB::table('course_permissions')
            ->where('status', CoursePermission::STATUS_APPROVED)
            ->update(['status' => '已通过']);

        DB::table('course_permissions')
            ->where('status', CoursePermission::STATUS_REJECTED)
            ->update(['status' => '已拒绝']);
    }
};
