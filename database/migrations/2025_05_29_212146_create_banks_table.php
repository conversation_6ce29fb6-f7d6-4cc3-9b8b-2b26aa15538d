<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banks', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('银行名称');
            $table->string('image')->nullable()->comment('银行图标');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('status')->default(true)->comment('状态：1启用，0禁用');
            $table->timestamps();

            $table->index(['status', 'sort']);
            $table->unique('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banks');
    }
};
