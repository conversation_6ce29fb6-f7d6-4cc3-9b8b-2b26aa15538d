<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父分类ID，0表示顶级分类');
            $table->string('name', 50)->comment('分类名称');
            $table->unsignedInteger('sort')->default(0)->comment('排序权重，数字越大越靠前');
            $table->unsignedTinyInteger('level')->default(1)->comment('分类层级，1为顶级分类');
            $table->string('path', 255)->default('')->comment('分类路径，格式如：,1,2,，方便查询');
            $table->timestamps();

            $table->index('parent_id');
            $table->index('sort');
            $table->index('level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_categories');
    }
};
