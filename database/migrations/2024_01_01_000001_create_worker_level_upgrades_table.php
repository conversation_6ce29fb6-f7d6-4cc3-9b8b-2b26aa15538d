<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('worker_level_upgrades', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('worker_id')->comment('师傅ID');
            $table->unsignedBigInteger('from_level_id')->nullable()->comment('原等级ID');
            $table->unsignedBigInteger('to_level_id')->comment('新等级ID');
            $table->decimal('income_at_upgrade', 10, 2)->comment('升级时的收益金额');
            $table->string('trigger_type', 20)->default('auto')->comment('触发类型：auto自动，manual手动');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();

            $table->index('worker_id');
            $table->index('from_level_id');
            $table->index('to_level_id');
            $table->index('created_at');

            $table->foreign('worker_id')->references('id')->on('workers')->onDelete('cascade');
            $table->foreign('from_level_id')->references('id')->on('worker_levels')->onDelete('set null');
            $table->foreign('to_level_id')->references('id')->on('worker_levels')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('worker_level_upgrades');
    }
};
