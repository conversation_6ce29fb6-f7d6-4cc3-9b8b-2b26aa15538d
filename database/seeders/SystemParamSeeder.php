<?php

namespace Database\Seeders;

use App\Models\SystemParam;
use Illuminate\Database\Seeder;

class SystemParamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $params = [
            // 基础配置
            [
                'name' => '网站名称',
                'key' => 'site_name',
                'value' => '本地服务平台',
                'group' => 'basic',
                'type' => 'text',
                'remark' => '网站名称，显示在浏览器标题、登录页等位置',
                'sort' => 0,
                'status' => 1
            ],
            [
                'name' => '网站Logo',
                'key' => 'site_logo',
                'value' => '/images/logo.png',
                'group' => 'basic',
                'type' => 'image',
                'remark' => '网站Logo图片',
                'sort' => 1,
                'status' => 1
            ],
            [
                'name' => '联系电话',
                'key' => 'contact_phone',
                'value' => '************',
                'group' => 'basic',
                'type' => 'text',
                'remark' => '客服联系电话',
                'sort' => 2,
                'status' => 1
            ],
            [
                'name' => '联系邮箱',
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'group' => 'basic',
                'type' => 'text',
                'remark' => '客服联系邮箱',
                'sort' => 3,
                'status' => 1
            ],

            // 支付配置
            [
                'name' => '微信支付AppID',
                'key' => 'wechat_pay_appid',
                'value' => '',
                'group' => 'payment',
                'type' => 'text',
                'remark' => '微信支付AppID',
                'sort' => 0,
                'status' => 1
            ],
            [
                'name' => '微信支付商户号',
                'key' => 'wechat_pay_mchid',
                'value' => '',
                'group' => 'payment',
                'type' => 'text',
                'remark' => '微信支付商户号',
                'sort' => 1,
                'status' => 1
            ],
            [
                'name' => '微信支付密钥',
                'key' => 'wechat_pay_key',
                'value' => '',
                'group' => 'payment',
                'type' => 'text',
                'remark' => '微信支付API密钥',
                'sort' => 2,
                'status' => 1
            ],
            [
                'name' => '支付宝AppID',
                'key' => 'alipay_appid',
                'value' => '',
                'group' => 'payment',
                'type' => 'text',
                'remark' => '支付宝AppID',
                'sort' => 3,
                'status' => 1
            ],
            [
                'name' => '支付宝应用私钥',
                'key' => 'alipay_private_key',
                'value' => '',
                'group' => 'payment',
                'type' => 'textarea',
                'remark' => '支付宝应用私钥',
                'sort' => 4,
                'status' => 1
            ],
            [
                'name' => '支付宝公钥',
                'key' => 'alipay_public_key',
                'value' => '',
                'group' => 'payment',
                'type' => 'textarea',
                'remark' => '支付宝公钥',
                'sort' => 5,
                'status' => 1
            ],

            // 短信配置
            [
                'name' => '短信服务提供商',
                'key' => 'sms_provider',
                'value' => 'aliyun',
                'group' => 'sms',
                'type' => 'select',
                'options' => json_encode(['aliyun' => '阿里云', 'tencent' => '腾讯云']),
                'remark' => '短信服务提供商',
                'sort' => 0,
                'status' => 1
            ],
            [
                'name' => '短信AccessKey',
                'key' => 'sms_access_key',
                'value' => '',
                'group' => 'sms',
                'type' => 'text',
                'remark' => '短信服务AccessKey',
                'sort' => 1,
                'status' => 1
            ],
            [
                'name' => '短信AccessSecret',
                'key' => 'sms_access_secret',
                'value' => '',
                'group' => 'sms',
                'type' => 'text',
                'remark' => '短信服务AccessSecret',
                'sort' => 2,
                'status' => 1
            ],
            [
                'name' => '短信签名',
                'key' => 'sms_sign_name',
                'value' => '本地服务',
                'group' => 'sms',
                'type' => 'text',
                'remark' => '短信签名名称',
                'sort' => 3,
                'status' => 1
            ],
            [
                'name' => '验证码模板ID',
                'key' => 'sms_template_code',
                'value' => '',
                'group' => 'sms',
                'type' => 'text',
                'remark' => '验证码短信模板ID',
                'sort' => 4,
                'status' => 1
            ],

            // 微信配置
            [
                'name' => '公众号AppID',
                'key' => 'wechat_appid',
                'value' => '',
                'group' => 'wechat',
                'type' => 'text',
                'remark' => '微信公众号AppID',
                'sort' => 0,
                'status' => 1
            ],
            [
                'name' => '公众号Secret',
                'key' => 'wechat_secret',
                'value' => '',
                'group' => 'wechat',
                'type' => 'text',
                'remark' => '微信公众号Secret',
                'sort' => 1,
                'status' => 1
            ],
            [
                'name' => '小程序AppID',
                'key' => 'miniapp_appid',
                'value' => '',
                'group' => 'wechat',
                'type' => 'text',
                'remark' => '微信小程序AppID',
                'sort' => 2,
                'status' => 1
            ],
            [
                'name' => '小程序Secret',
                'key' => 'miniapp_secret',
                'value' => '',
                'group' => 'wechat',
                'type' => 'text',
                'remark' => '微信小程序Secret',
                'sort' => 3,
                'status' => 1
            ],

            // APP配置
            [
                'name' => '新用户注册欢迎语',
                'key' => 'user_welcome_message',
                'value' => '欢迎加入本地服务平台，您可以在这里享受到专业的上门服务！',
                'group' => 'app',
                'type' => 'textarea',
                'remark' => '新用户注册后显示的欢迎语',
                'sort' => 0,
                'status' => 1
            ],
            [
                'name' => '服务评分开关',
                'key' => 'enable_service_rating',
                'value' => '1',
                'group' => 'app',
                'type' => 'switch',
                'remark' => '是否开启服务评分功能',
                'sort' => 1,
                'status' => 1
            ],
            [
                'name' => '师傅接单范围(公里)',
                'key' => 'worker_service_radius',
                'value' => '20',
                'group' => 'app',
                'type' => 'number',
                'remark' => '师傅接单的最大距离范围，单位公里',
                'sort' => 2,
                'status' => 1
            ],
            [
                'name' => '应用二维码',
                'key' => 'app_qrcode',
                'value' => '',
                'group' => 'app',
                'type' => 'image',
                'remark' => '应用下载二维码图片',
                'sort' => 3,
                'status' => 1
            ],
        ];

        foreach ($params as $param) {
            SystemParam::updateOrCreate(
                ['key' => $param['key']],
                $param
            );
        }
    }
}
