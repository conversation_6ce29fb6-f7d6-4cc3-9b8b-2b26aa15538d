<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Bank;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banks = [
            ['name' => '中国工商银行', 'sort' => 100, 'status' => true],
            ['name' => '中国建设银行', 'sort' => 99, 'status' => true],
            ['name' => '中国农业银行', 'sort' => 98, 'status' => true],
            ['name' => '中国银行', 'sort' => 97, 'status' => true],
            ['name' => '交通银行', 'sort' => 96, 'status' => true],
            ['name' => '招商银行', 'sort' => 95, 'status' => true],
            ['name' => '中国民生银行', 'sort' => 94, 'status' => true],
            ['name' => '中信银行', 'sort' => 93, 'status' => true],
            ['name' => '中国光大银行', 'sort' => 92, 'status' => true],
            ['name' => '华夏银行', 'sort' => 91, 'status' => true],
            ['name' => '广发银行', 'sort' => 90, 'status' => true],
            ['name' => '平安银行', 'sort' => 89, 'status' => true],
            ['name' => '兴业银行', 'sort' => 88, 'status' => true],
            ['name' => '浦发银行', 'sort' => 87, 'status' => true],
            ['name' => '上海银行', 'sort' => 86, 'status' => true],
            ['name' => '北京银行', 'sort' => 85, 'status' => true],
            ['name' => '江苏银行', 'sort' => 84, 'status' => true],
            ['name' => '宁波银行', 'sort' => 83, 'status' => true],
            ['name' => '南京银行', 'sort' => 82, 'status' => true],
            ['name' => '杭州银行', 'sort' => 81, 'status' => true],
            ['name' => '中国邮政储蓄银行', 'sort' => 80, 'status' => true],
        ];

        foreach ($banks as $bank) {
            Bank::firstOrCreate(['name' => $bank['name']], $bank);
        }
    }
}
