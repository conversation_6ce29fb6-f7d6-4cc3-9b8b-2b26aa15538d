<?php

namespace Database\Seeders;

use App\Models\AppointmentTimeSlot;
use Illuminate\Database\Seeder;

class AppointmentTimeSlotSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $timeSlots = [
            // 周一到周五的下午时间段
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '下午',
                'start_time' => '14:00',
                'end_time' => '15:00',
                'status' => 1,
                'sort' => 10,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '下午',
                'start_time' => '15:00',
                'end_time' => '16:00',
                'status' => 1,
                'sort' => 9,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '下午',
                'start_time' => '16:00',
                'end_time' => '17:00',
                'status' => 1,
                'sort' => 8,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '下午',
                'start_time' => '17:00',
                'end_time' => '18:00',
                'status' => 1,
                'sort' => 7,
            ],

            // 周一到周五的晚上时间段
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '晚上',
                'start_time' => '18:00',
                'end_time' => '19:00',
                'status' => 1,
                'sort' => 6,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '晚上',
                'start_time' => '19:00',
                'end_time' => '20:00',
                'status' => 1,
                'sort' => 5,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '晚上',
                'start_time' => '20:00',
                'end_time' => '21:00',
                'status' => 1,
                'sort' => 4,
            ],
            [
                'day_name' => '周一',
                'day_of_week' => 1,
                'time_period' => '晚上',
                'start_time' => '21:00',
                'end_time' => '22:00',
                'status' => 1,
                'sort' => 3,
            ],

            // 复制周一的时间段到周二到周五
            // 周二
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '下午',
                'start_time' => '14:00',
                'end_time' => '15:00',
                'status' => 1,
                'sort' => 10,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '下午',
                'start_time' => '15:00',
                'end_time' => '16:00',
                'status' => 1,
                'sort' => 9,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '下午',
                'start_time' => '16:00',
                'end_time' => '17:00',
                'status' => 1,
                'sort' => 8,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '下午',
                'start_time' => '17:00',
                'end_time' => '18:00',
                'status' => 1,
                'sort' => 7,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '晚上',
                'start_time' => '18:00',
                'end_time' => '19:00',
                'status' => 1,
                'sort' => 6,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '晚上',
                'start_time' => '19:00',
                'end_time' => '20:00',
                'status' => 1,
                'sort' => 5,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '晚上',
                'start_time' => '20:00',
                'end_time' => '21:00',
                'status' => 1,
                'sort' => 4,
            ],
            [
                'day_name' => '周二',
                'day_of_week' => 2,
                'time_period' => '晚上',
                'start_time' => '21:00',
                'end_time' => '22:00',
                'status' => 1,
                'sort' => 3,
            ],

            // 周三
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '下午',
                'start_time' => '14:00',
                'end_time' => '15:00',
                'status' => 1,
                'sort' => 10,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '下午',
                'start_time' => '15:00',
                'end_time' => '16:00',
                'status' => 1,
                'sort' => 9,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '下午',
                'start_time' => '16:00',
                'end_time' => '17:00',
                'status' => 1,
                'sort' => 8,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '下午',
                'start_time' => '17:00',
                'end_time' => '18:00',
                'status' => 1,
                'sort' => 7,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '晚上',
                'start_time' => '18:00',
                'end_time' => '19:00',
                'status' => 1,
                'sort' => 6,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '晚上',
                'start_time' => '19:00',
                'end_time' => '20:00',
                'status' => 1,
                'sort' => 5,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '晚上',
                'start_time' => '20:00',
                'end_time' => '21:00',
                'status' => 1,
                'sort' => 4,
            ],
            [
                'day_name' => '周三',
                'day_of_week' => 3,
                'time_period' => '晚上',
                'start_time' => '21:00',
                'end_time' => '22:00',
                'status' => 1,
                'sort' => 3,
            ],
        ];

        foreach ($timeSlots as $timeSlot) {
            AppointmentTimeSlot::updateOrCreate(
                [
                    'day_of_week' => $timeSlot['day_of_week'],
                    'time_period' => $timeSlot['time_period'],
                    'start_time' => $timeSlot['start_time'],
                    'end_time' => $timeSlot['end_time'],
                ],
                $timeSlot
            );
        }
    }
}
