<?php

namespace Database\Seeders;

use App\Models\Area;
use Illuminate\Database\Seeder;

class AreaSeeder extends Seeder
{
    /**
     * 运行地区表数据填充
     *
     * @return void
     */
    public function run(): void
    {
        // 清空现有数据
        Area::truncate();

        // 1. 添加省份
        $provinces = [
            ['name' => '北京市', 'code' => '110000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '天津市', 'code' => '120000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '河北省', 'code' => '130000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '山西省', 'code' => '140000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '内蒙古自治区', 'code' => '150000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '辽宁省', 'code' => '210000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '吉林省', 'code' => '220000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '黑龙江省', 'code' => '230000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '上海市', 'code' => '310000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '江苏省', 'code' => '320000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '浙江省', 'code' => '330000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '安徽省', 'code' => '340000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '福建省', 'code' => '350000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '江西省', 'code' => '360000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '山东省', 'code' => '370000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '河南省', 'code' => '410000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '湖北省', 'code' => '420000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '湖南省', 'code' => '430000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '广东省', 'code' => '440000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '广西壮族自治区', 'code' => '450000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '海南省', 'code' => '460000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '重庆市', 'code' => '500000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '四川省', 'code' => '510000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '贵州省', 'code' => '520000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '云南省', 'code' => '530000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '西藏自治区', 'code' => '540000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '陕西省', 'code' => '610000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '甘肃省', 'code' => '620000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '青海省', 'code' => '630000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '宁夏回族自治区', 'code' => '640000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '新疆维吾尔自治区', 'code' => '650000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '台湾省', 'code' => '710000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '香港特别行政区', 'code' => '810000', 'level' => Area::LEVEL_PROVINCE],
            ['name' => '澳门特别行政区', 'code' => '820000', 'level' => Area::LEVEL_PROVINCE],
        ];

        foreach ($provinces as $province) {
            Area::create([
                'name' => $province['name'],
                'parent_id' => 0,
                'level' => $province['level'],
                'code' => $province['code'],
                'status' => 1
            ]);
        }

        // 2. 添加主要城市
        // 北京下属区/县
        $beijingId = Area::where('name', '北京市')->value('id');
        $beijingDistricts = [
            ['name' => '东城区', 'code' => '110101', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '西城区', 'code' => '110102', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '朝阳区', 'code' => '110105', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '海淀区', 'code' => '110108', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '丰台区', 'code' => '110106', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '石景山区', 'code' => '110107', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '通州区', 'code' => '110112', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '顺义区', 'code' => '110113', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '昌平区', 'code' => '110114', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '大兴区', 'code' => '110115', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '房山区', 'code' => '110111', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '门头沟区', 'code' => '110109', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '怀柔区', 'code' => '110116', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '平谷区', 'code' => '110117', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '密云区', 'code' => '110118', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
            ['name' => '延庆区', 'code' => '110119', 'parent_id' => $beijingId, 'level' => Area::LEVEL_CITY],
        ];

        foreach ($beijingDistricts as $district) {
            Area::create([
                'name' => $district['name'],
                'parent_id' => $district['parent_id'],
                'level' => $district['level'],
                'code' => $district['code'],
                'status' => 1
            ]);
        }

        // 上海下属区/县
        $shanghaiId = Area::where('name', '上海市')->value('id');
        $shanghaiDistricts = [
            ['name' => '黄浦区', 'code' => '310101', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '徐汇区', 'code' => '310104', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '长宁区', 'code' => '310105', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '静安区', 'code' => '310106', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '普陀区', 'code' => '310107', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '虹口区', 'code' => '310109', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '杨浦区', 'code' => '310110', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '浦东新区', 'code' => '310115', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '闵行区', 'code' => '310112', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '宝山区', 'code' => '310113', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '嘉定区', 'code' => '310114', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '金山区', 'code' => '310116', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '松江区', 'code' => '310117', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '青浦区', 'code' => '310118', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '奉贤区', 'code' => '310120', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
            ['name' => '崇明区', 'code' => '310151', 'parent_id' => $shanghaiId, 'level' => Area::LEVEL_CITY],
        ];

        foreach ($shanghaiDistricts as $district) {
            Area::create([
                'name' => $district['name'],
                'parent_id' => $district['parent_id'],
                'level' => $district['level'],
                'code' => $district['code'],
                'status' => 1
            ]);
        }

        // 广东省下属城市
        $guangdongId = Area::where('name', '广东省')->value('id');
        $guangdongCities = [
            ['name' => '广州市', 'code' => '440100', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '深圳市', 'code' => '440300', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '珠海市', 'code' => '440400', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '汕头市', 'code' => '440500', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '佛山市', 'code' => '440600', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '韶关市', 'code' => '440200', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '湛江市', 'code' => '440800', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '肇庆市', 'code' => '441200', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '江门市', 'code' => '440700', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '茂名市', 'code' => '440900', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '惠州市', 'code' => '441300', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '梅州市', 'code' => '441400', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '汕尾市', 'code' => '441500', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '河源市', 'code' => '441600', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '阳江市', 'code' => '441700', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '清远市', 'code' => '441800', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '东莞市', 'code' => '441900', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '中山市', 'code' => '442000', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '潮州市', 'code' => '445100', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '揭阳市', 'code' => '445200', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
            ['name' => '云浮市', 'code' => '445300', 'parent_id' => $guangdongId, 'level' => Area::LEVEL_CITY],
        ];

        foreach ($guangdongCities as $city) {
            Area::create([
                'name' => $city['name'],
                'parent_id' => $city['parent_id'],
                'level' => $city['level'],
                'code' => $city['code'],
                'status' => 1
            ]);
        }

        // 3. 添加部分区县级地区
        // 广州市下属区县
        $guangzhouId = Area::where('name', '广州市')->value('id');
        $guangzhouDistricts = [
            ['name' => '越秀区', 'code' => '440104', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '海珠区', 'code' => '440105', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '荔湾区', 'code' => '440103', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '天河区', 'code' => '440106', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '白云区', 'code' => '440111', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '黄埔区', 'code' => '440112', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '番禺区', 'code' => '440113', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '花都区', 'code' => '440114', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '南沙区', 'code' => '440115', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '从化区', 'code' => '440117', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '增城区', 'code' => '440118', 'parent_id' => $guangzhouId, 'level' => Area::LEVEL_DISTRICT],
        ];

        foreach ($guangzhouDistricts as $district) {
            Area::create([
                'name' => $district['name'],
                'parent_id' => $district['parent_id'],
                'level' => $district['level'],
                'code' => $district['code'],
                'status' => 1
            ]);
        }

        // 深圳市下属区县
        $shenzhenId = Area::where('name', '深圳市')->value('id');
        $shenzhenDistricts = [
            ['name' => '福田区', 'code' => '440304', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '罗湖区', 'code' => '440303', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '南山区', 'code' => '440305', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '宝安区', 'code' => '440306', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '龙岗区', 'code' => '440307', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '盐田区', 'code' => '440308', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '龙华区', 'code' => '440309', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '坪山区', 'code' => '440310', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
            ['name' => '光明区', 'code' => '440311', 'parent_id' => $shenzhenId, 'level' => Area::LEVEL_DISTRICT],
        ];

        foreach ($shenzhenDistricts as $district) {
            Area::create([
                'name' => $district['name'],
                'parent_id' => $district['parent_id'],
                'level' => $district['level'],
                'code' => $district['code'],
                'status' => 1
            ]);
        }
    }
}
