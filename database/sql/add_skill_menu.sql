-- 添加技能管理菜单项
-- 假设服务管理菜单的parent_id为2，你可以根据实际情况调整
-- 以下SQL添加技能管理菜单到服务管理菜单下

-- 查找服务管理菜单ID
-- SELECT id FROM admin_menu WHERE title = '服务管理' LIMIT 1;

-- 添加技能管理菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `created_at`, `updated_at`)
VALUES
-- 假设服务管理的ID是8，技能管理放在服务管理下面，你需要根据实际情况修改parent_id
(8, 8, '技能管理', 'fa-tasks', 'skills', NOW(), NOW());

-- 添加师傅技能认证菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `created_at`, `updated_at`)
VALUES
-- 假设师傅管理的ID是9，师傅技能认证放在师傅管理下面，你需要根据实际情况修改parent_id
(9, 9, '技能认证管理', 'fa-certificate', 'worker-skills', NOW(), NOW());

-- 如果想放在顶级菜单
-- INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `created_at`, `updated_at`)
-- VALUES
-- (0, 8, '技能管理', 'fa-tasks', 'skills', NOW(), NOW());

-- 为管理员角色添加技能管理对应权限（假设admin角色ID为1）
INSERT INTO `admin_role_menu` (`role_id`, `menu_id`, `created_at`, `updated_at`)
SELECT 1, id, NOW(), NOW() FROM `admin_menu` WHERE `uri` = 'skills' AND `title` = '技能管理';

-- 为管理员角色添加技能认证对应权限
INSERT INTO `admin_role_menu` (`role_id`, `menu_id`, `created_at`, `updated_at`)
SELECT 1, id, NOW(), NOW() FROM `admin_menu` WHERE `uri` = 'worker-skills' AND `title` = '技能认证管理';

-- 添加技能管理权限项
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `order`, `parent_id`, `created_at`, `updated_at`)
VALUES
('技能管理', 'skills', '', '/skills*', 8, 0, NOW(), NOW());

-- 添加技能认证权限项
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `order`, `parent_id`, `created_at`, `updated_at`)
VALUES
('技能认证管理', 'worker-skills', '', '/worker-skills*', 9, 0, NOW(), NOW());

-- 关联技能管理权限到角色（假设admin角色ID为1）
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 1, id, NOW(), NOW() FROM `admin_permissions` WHERE `slug` = 'skills';

-- 关联技能认证权限到角色
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 1, id, NOW(), NOW() FROM `admin_permissions` WHERE `slug` = 'worker-skills';

-- 关联技能管理权限到菜单
INSERT INTO `admin_permission_menu` (`permission_id`, `menu_id`, `created_at`, `updated_at`)
SELECT p.id, m.id, NOW(), NOW() FROM `admin_permissions` p, `admin_menu` m
WHERE p.slug = 'skills' AND m.uri = 'skills' AND m.title = '技能管理';

-- 关联技能认证权限到菜单
INSERT INTO `admin_permission_menu` (`permission_id`, `menu_id`, `created_at`, `updated_at`)
SELECT p.id, m.id, NOW(), NOW() FROM `admin_permissions` p, `admin_menu` m
WHERE p.slug = 'worker-skills' AND m.uri = 'worker-skills' AND m.title = '技能认证管理';
