-- 添加技能表模拟数据
-- 注意：请确保service_categories表中存在对应的分类ID
-- 执行前请检查分类ID是否存在，否则可能导致外键约束错误

-- 电器维修分类（假设ID为1）
INSERT INTO `skills` (`name`, `category_id`, `deposit_amount`, `after_sale_days`, `created_at`, `updated_at`)
VALUES
('空调维修', 1, 500.00, 30, NOW(), NOW()),
('冰箱维修', 1, 300.00, 30, NOW(), NOW()),
('洗衣机维修', 1, 300.00, 30, NOW(), NOW()),
('热水器维修', 1, 400.00, 45, NOW(), NOW()),
('电视机维修', 1, 200.00, 30, NOW(), NOW()),
('微波炉维修', 1, 100.00, 15, NOW(), NOW());

-- 水电维修分类（假设ID为2）
INSERT INTO `skills` (`name`, `category_id`, `deposit_amount`, `after_sale_days`, `created_at`, `updated_at`)
VALUES
('水管安装', 2, 200.00, 90, NOW(), NOW()),
('水龙头维修', 2, 50.00, 30, NOW(), NOW()),
('下水道疏通', 2, 100.00, 15, NOW(), NOW()),
('电路检修', 2, 300.00, 60, NOW(), NOW()),
('灯具安装', 2, 100.00, 30, NOW(), NOW()),
('电路布线', 2, 500.00, 180, NOW(), NOW());

-- 家具维修分类（假设ID为3）
INSERT INTO `skills` (`name`, `category_id`, `deposit_amount`, `after_sale_days`, `created_at`, `updated_at`)
VALUES
('沙发翻新', 3, 300.00, 90, NOW(), NOW()),
('桌椅修复', 3, 150.00, 60, NOW(), NOW()),
('家具组装', 3, 200.00, 30, NOW(), NOW()),
('橱柜安装', 3, 400.00, 120, NOW(), NOW()),
('地板维修', 3, 350.00, 90, NOW(), NOW()),
('门窗维修', 3, 200.00, 60, NOW(), NOW());

-- 墙面装修分类（假设ID为4）
INSERT INTO `skills` (`name`, `category_id`, `deposit_amount`, `after_sale_days`, `created_at`, `updated_at`)
VALUES
('墙面粉刷', 4, 200.00, 180, NOW(), NOW()),
('壁纸施工', 4, 300.00, 120, NOW(), NOW()),
('瓷砖铺贴', 4, 400.00, 180, NOW(), NOW()),
('墙面防水', 4, 500.00, 365, NOW(), NOW()),
('墙面裂缝修补', 4, 150.00, 90, NOW(), NOW()),
('天花板修复', 4, 300.00, 120, NOW(), NOW());

-- 清洁服务分类（假设ID为5）
INSERT INTO `skills` (`name`, `category_id`, `deposit_amount`, `after_sale_days`, `created_at`, `updated_at`)
VALUES
('深度清洁', 5, 100.00, 7, NOW(), NOW()),
('油烟机清洗', 5, 150.00, 30, NOW(), NOW()),
('地毯清洗', 5, 200.00, 30, NOW(), NOW()),
('沙发清洗', 5, 150.00, 15, NOW(), NOW()),
('窗户清洁', 5, 100.00, 7, NOW(), NOW()),
('空调清洗', 5, 200.00, 90, NOW(), NOW());

-- 注意：执行此SQL前，请确保service_categories表中有对应的category_id
-- 如果分类ID不存在，请修改上述SQL中的category_id值
-- 或者先向service_categories表中插入相应的分类数据

-- 此外，如果category_id允许为NULL，可以取消外键约束后插入
-- 但不建议这样做，因为会造成数据关联性问题
