# 短信服务使用说明

## 快速开始

### 1. 开发环境配置（推荐）

在 `.env` 文件中添加：
```env
SMS_DRIVER=log
```

这样短信内容会输出到日志文件中，方便开发调试。

### 2. 生产环境配置

在 `.env` 文件中添加：
```env
SMS_DRIVER=aliyun
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_SMS_SIGN_NAME=【您的应用名称】
ALIYUN_SMS_TEMPLATE_VERIFICATION=SMS_123456789
```

### 3. API使用

发送验证码：
```bash
curl -X POST http://localhost/api/user/send-verify-code \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "type": "register"
  }'
```

支持的类型：
- `register` - 注册验证码
- `login` - 登录验证码  
- `reset` - 重置密码验证码
- `update` - 更新手机号验证码

### 4. 查看日志（开发环境）

```bash
tail -f storage/logs/laravel.log | grep "短信"
```

## 功能特性

✅ **已测试验证**：
- 验证码生成（6位数字）
- 手机号格式验证
- 发送频率限制（1分钟间隔）
- 开发环境日志模拟
- 生产环境阿里云短信发送

✅ **安全机制**：
- 参数严格验证
- 发送频率控制
- 错误处理和日志记录
- 验证码5分钟过期

## 阿里云配置步骤

### 1. 开通服务
访问[阿里云短信服务控制台](https://dysms.console.aliyun.com/)开通服务

### 2. 创建AccessKey
- 进入RAM访问控制台
- 创建RAM用户，勾选"编程访问"
- 授予 `AliyunDysmsFullAccess` 权限

### 3. 申请短信签名
- 在短信控制台申请签名
- 提交营业执照等资质文件
- 等待审核通过（1-2工作日）

### 4. 创建短信模板
- 选择"验证码"类型
- 模板示例：`您的验证码是：${code}，5分钟内有效，请勿泄露给他人。`
- 提交审核，获得模板Code

## 常见问题

**Q: 为什么收不到短信？**
A: 检查以下配置：
1. SMS_DRIVER 是否设置为 aliyun
2. 阿里云配置是否正确
3. 短信签名和模板是否审核通过
4. 账户是否有足够余额

**Q: 开发环境如何调试？**
A: 设置 `SMS_DRIVER=log`，短信内容会写入日志文件

**Q: 如何查看发送状态？**
A: 查看 `storage/logs/laravel.log` 文件中的详细日志

## 详细文档

完整的集成说明请参考：[docs/sms-service-integration.md](docs/sms-service-integration.md)
