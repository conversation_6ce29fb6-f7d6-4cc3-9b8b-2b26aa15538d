<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 微信支付回调中间件
 *
 * 处理微信支付回调的XML数据，确保能正确解析回调内容
 */
class WechatPaymentCallbackMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 只处理微信支付回调
        $paymentMethod = $request->input('payment_method');
        $isWechatPayment = in_array($paymentMethod, ['wechat', 'wechat_miniprogram', 'wechat_app', 'worker_wechat_miniprogram', 'worker_wechat_app']);

        // 处理条件：
        // 1. 明确指定为微信支付方式
        // 2. 或者是支付回调路径但没有指定支付方式（默认为微信支付）
        if ($isWechatPayment ||
            ($request->is('*/payments/callback*') &&
             $request->method() === 'POST' &&
             empty($paymentMethod))) {

            // 获取原始XML数据
            $rawInput = file_get_contents('php://input');

            Log::info('微信支付回调中间件处理', [
                'raw_input' => $rawInput,
                'content_type' => $request->header('Content-Type'),
                'request_method' => $request->method(),
                'url' => $request->fullUrl(),
                'query_params' => $request->query(),
                'post_data' => $request->all()
            ]);

            // 如果有XML数据但POST数据为空，尝试解析XML
            if (!empty($rawInput) && empty($request->all()) &&
                (strpos($rawInput, '<xml>') !== false || strpos($rawInput, '<?xml') !== false)) {

                try {
                    // 解析XML数据
                    $xml = simplexml_load_string($rawInput, 'SimpleXMLElement', LIBXML_NOCDATA);

                    if ($xml !== false) {
                        $xmlArray = json_decode(json_encode($xml), true);

                        Log::info('成功解析微信支付回调XML', [
                            'parsed_data' => $xmlArray
                        ]);

                        // 将解析后的数据合并到请求中
                        $request->merge($xmlArray);
                    }
                } catch (\Exception $e) {
                    Log::error('解析微信支付回调XML失败', [
                        'error' => $e->getMessage(),
                        'raw_input' => $rawInput
                    ]);
                }
            }

            // 确保payment_method参数存在
            if (!$request->has('payment_method')) {
                $request->merge(['payment_method' => 'wechat']);
            }
        }

        return $next($request);
    }
}
