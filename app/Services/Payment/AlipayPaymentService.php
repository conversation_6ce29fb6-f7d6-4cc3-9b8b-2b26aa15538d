<?php

namespace App\Services\Payment;

use App\Models\Payment;
use App\Services\Payment\Contracts\PaymentServiceInterface;
use Illuminate\Support\Facades\Log;

/**
 * 支付宝支付基础服务类
 */
class AlipayPaymentService extends AbstractPaymentService implements PaymentServiceInterface
{
    /**
     * 获取支付宝SDK配置
     *
     * @return array
     */
    protected function getConfig(): array
    {
        return config('payment.alipay', []);
    }

    /**
     * 获取支付宝SDK实例
     *
     * @return \Alipay\EasySDK\Kernel\Client
     */
    protected function getSdk()
    {
        $config = $this->getConfig();

        // 从配置中获取支付宝所需的参数
        $options = new \Alipay\EasySDK\Kernel\Config();
        $options->protocol = 'https';
        $options->gatewayHost = 'openapi.alipay.com';
        $options->signType = 'RSA2';

        // 请替换为您的应用ID
        $options->appId = $config['app_id'] ?? '';

        // 请替换为您的应用私钥
        $options->merchantPrivateKey = $config['private_key'] ?? '';

        // 请替换为您的支付宝公钥
        $options->alipayPublicKey = $config['alipay_public_key'] ?? '';

        // 如果使用了AES加密，则需要配置
        if (isset($config['encrypt_key']) && !empty($config['encrypt_key'])) {
            $options->encryptKey = $config['encrypt_key'];
        }

        Log::info('alipay_config' . json_encode($options));

        $options->notifyUrl = route('wechat.payments.callback', ['payment_method' => 'alipay']);

        // 初始化SDK工厂
        return \Alipay\EasySDK\Kernel\Factory::setOptions($options);
    }

    /**
     * 获取订单主题
     *
     * @param Payment $payment
     * @return string
     */
    protected function getOrderSubject(Payment $payment): string
    {
        $subject = '订单支付';

        switch ($payment->payment_type) {
            case Payment::TYPE_BOOKING:
                $subject = '预约费支付';
                break;
            case Payment::TYPE_SERVICE:
                $subject = '服务费支付';
                break;
            case Payment::TYPE_MATERIAL:
                $subject = '材料费支付';
                break;
            case Payment::TYPE_RECHARGE:
                $subject = '账户充值';
                break;
            case Payment::TYPE_ANNUAL_PACKAGE:
                $subject = '年费套餐购买';
                break;
        }

        return $subject;
    }

    /**
     * 创建支付宝订单
     *
     * @param Payment $payment
     * @param array $options
     * @return array
     */
    public function createOrder(Payment $payment, array $options = []): array
    {
        try {
            // 默认创建APP支付
            $config = $this->getConfig();
            $alipaySdk = $this->getSdk();

            // 构建支付参数
            $subject = $this->getOrderSubject($payment);
            $outTradeNo = $payment->payment_no;
            $totalAmount = $payment->amount;

            // 设置异步通知地址
            $notifyUrl = route('wechat.payments.callback', ['payment_method' => 'alipay']);

            // 发起支付宝支付
            $result = $alipaySdk->payment()->app()
                ->pay($subject, $outTradeNo, $totalAmount);

            if ($result) {
                return [
                    'success' => true,
                    'data' => [
                        'payment_id' => $payment->id,
                        'payment_no' => $payment->payment_no,
                        'pay_params' => $result,
                        'payment_method' => 'alipay'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '创建支付宝支付订单失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('创建支付宝支付订单异常', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '创建支付宝支付订单异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 查询支付宝订单
     *
     * @param Payment $payment
     * @return array
     */
    public function queryOrder(Payment $payment): array
    {
        try {
            $alipaySdk = $this->getSdk();
            $result = $alipaySdk->payment()->common()->query($payment->payment_no);

            if ($result->code == '10000' && $result->tradeStatus == 'TRADE_SUCCESS') {
                return [
                    'success' => true,
                    'data' => [
                        'trade_no' => $result->tradeNo,
                        'paid_at' => $result->sendPayDate,
                        'amount' => $result->totalAmount,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result->msg ?? '查询支付宝订单失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('查询支付宝订单异常', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '查询支付宝订单异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理支付宝回调
     *
     * @param array $data
     * @return array
     */
    public function handleCallback(array $data): array
    {
        try {
            if ($this->verifyCallback($data)) {
                // 获取交易状态
                $tradeStatus = $data['trade_status'] ?? '';

                // 只有交易成功或交易完成的状态才视为支付成功
                if ($tradeStatus === 'TRADE_SUCCESS' || $tradeStatus === 'TRADE_FINISHED') {
                    // 查找对应的支付记录
                    $payment = Payment::where('payment_no', $data['out_trade_no'])->first();

                    if ($payment) {
                        // 更新支付状态
                        $payment->status = Payment::STATUS_PAID;
                        $payment->paid_at = now();
                        $payment->trade_no = $data['trade_no'];
                        $payment->save();

                        // 处理支付成功后的业务逻辑
                        $this->handlePaymentSuccess($payment);

                        return [
                            'success' => true,
                            'message' => '支付成功',
                            'response' => 'success'
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => '未找到对应的支付记录',
                            'response' => 'success'  // 即使找不到记录也返回success，避免支付宝重复通知
                        ];
                    }
                } else {
                    return [
                        'success' => false,
                        'message' => '交易状态不是成功状态',
                        'response' => 'success'  // 返回success，避免支付宝重复通知
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => '回调验证失败',
                    'response' => 'fail'
                ];
            }
        } catch (\Exception $e) {
            Log::error('处理支付宝回调异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => '处理支付宝回调异常: ' . $e->getMessage(),
                'response' => 'fail'
            ];
        }
    }

    /**
     * 验证支付宝回调
     *
     * @param array $data
     * @return bool
     */
    public function verifyCallback(array $data): bool
    {
        try {
            Log::info('verifyCallback'. json_encode($data));

            $alipaySdk = $this->getSdk();
            return $alipaySdk->payment()->common()->verifyNotify($data);
        } catch (\Exception $e) {
            Log::error('验证支付宝回调异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return false;
        }
    }

    /**
     * 申请退款
     *
     * @param Payment $payment
     * @param float $refundAmount
     * @param string $refundReason
     * @return array
     */
    public function refund(Payment $payment, float $refundAmount, string $refundReason = ''): array
    {
        try {
            $alipaySdk = $this->getSdk();
            $outRequestNo = 'RF' . date('YmdHis') . rand(1000, 9999);

            $result = $alipaySdk->payment()->common()
                ->refund($payment->payment_no, $refundAmount, $refundReason, $outRequestNo);

            if ($result->code == '10000') {
                return [
                    'success' => true,
                    'data' => [
                        'refund_no' => $outRequestNo,
                        'trade_no' => $result->tradeNo,
                        'refund_amount' => $result->refundFee,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result->msg ?? '申请支付宝退款失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('申请支付宝退款异常', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '申请支付宝退款异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 查询退款
     *
     * @param string $refundNo
     * @return array
     */
    public function queryRefund(string $refundNo): array
    {
        try {
            $alipaySdk = $this->getSdk();
            $result = $alipaySdk->payment()->common()->queryRefund($refundNo);

            if ($result->code == '10000') {
                return [
                    'success' => true,
                    'data' => [
                        'refund_no' => $refundNo,
                        'trade_no' => $result->tradeNo,
                        'refund_amount' => $result->refundAmount,
                        'refund_status' => $result->refundStatus,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result->msg ?? '查询支付宝退款失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('查询支付宝退款异常', [
                'refund_no' => $refundNo,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '查询支付宝退款异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取支付方式
     *
     * @return string
     */
    public function getPaymentMethod(): string
    {
        return 'alipay';
    }
}
