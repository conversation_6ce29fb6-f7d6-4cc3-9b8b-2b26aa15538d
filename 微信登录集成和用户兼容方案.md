# 微信登录集成和用户兼容方案

## 项目概述

本方案旨在为现有的本地服务平台集成微信登录功能，支持静默登录和授权登录两种模式，并解决多平台用户账号兼容问题，以手机号作为用户唯一标识实现账号合并。

## 一、当前系统分析

### 1.1 现有认证机制
- 手机号+密码登录
- 基础微信登录（仅支持已绑定用户）
- JWT Token认证机制
- 短信验证码注册/重置密码

### 1.2 现有用户表结构
基于AuthController分析，当前用户表包含以下字段：
- `id`: 用户主键
- `phone`: 手机号（唯一标识）
- `password`: 密码
- `name`: 真实姓名
- `nickname`: 昵称
- `avatar`: 头像
- `openid`: 微信OpenID
- `status`: 账号状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 1.3 现有问题
1. 微信登录功能不完整，仅支持已绑定用户
2. 缺少微信静默登录和授权登录的区分
3. 没有用户合并机制
4. 缺少UnionID支持（跨平台用户识别）
5. 缺少微信用户信息存储

## 二、技术方案设计

### 2.1 微信登录类型说明

#### 2.1.1 静默登录（wx.login）
- **特点**: 只能获取用户OpenID，无法获取用户基本信息
- **使用场景**: 
  - 已注册用户的快速登录
  - 小程序启动时的自动登录检测
- **数据获取**: 仅OpenID和SessionKey

#### 2.1.2 授权登录（wx.getUserProfile）
- **特点**: 需要用户主动授权，可获取用户基本信息
- **使用场景**: 
  - 新用户注册
  - 老用户绑定微信
  - 更新用户微信信息
- **数据获取**: OpenID、UnionID、昵称、头像等

### 2.2 数据库设计优化

#### 2.2.1 用户表字段扩展
需要为users表添加以下字段：

```sql
ALTER TABLE users ADD COLUMN IF NOT EXISTS unionid VARCHAR(64) COMMENT '微信UnionID';
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_nickname VARCHAR(100) COMMENT '微信昵称';
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_avatar VARCHAR(255) COMMENT '微信头像';
ALTER TABLE users ADD COLUMN IF NOT EXISTS platform VARCHAR(20) DEFAULT 'app' COMMENT '注册平台：miniprogram,app,h5';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_platform VARCHAR(20) COMMENT '最后登录平台';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP NULL COMMENT '最后登录时间';

-- 添加索引
CREATE INDEX idx_users_unionid ON users(unionid);
CREATE INDEX idx_users_platform ON users(platform);
```

#### 2.2.2 微信登录日志表
创建微信登录相关的日志表：

```sql
CREATE TABLE wechat_login_logs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NULL COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信OpenID',
    unionid VARCHAR(64) NULL COMMENT '微信UnionID',
    login_type ENUM('silent', 'authorized') NOT NULL COMMENT '登录类型：静默/授权',
    platform VARCHAR(20) NOT NULL COMMENT '登录平台',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    result ENUM('success', 'failed', 'need_binding') NOT NULL COMMENT '登录结果',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_unionid (unionid),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信登录日志表';
```

### 2.3 用户合并策略

#### 2.3.1 合并规则
以手机号为最终用户唯一标识，按以下优先级合并：

1. **已有手机号+密码账号，后绑定微信**
   - 保留原账号ID和所有数据
   - 添加微信信息到现有账号

2. **先有微信账号，后绑定手机号**
   - 保留微信账号ID和数据
   - 添加手机号和密码

3. **同时存在手机号账号和微信账号**
   - 以手机号账号为主账号
   - 将微信账号数据迁移到手机号账号
   - 删除或禁用微信账号

#### 2.3.2 数据迁移策略
当发现需要合并账号时：

```sql
-- 示例：将微信账号(ID=B)的数据迁移到手机号账号(ID=A)
-- 1. 迁移订单数据
UPDATE orders SET user_id = A WHERE user_id = B;

-- 2. 迁移钱包数据（合并余额）
UPDATE user_wallets 
SET balance = balance + (SELECT balance FROM user_wallets WHERE user_id = B),
    frozen_balance = frozen_balance + (SELECT frozen_balance FROM user_wallets WHERE user_id = B)
WHERE user_id = A;

-- 3. 迁移其他关联数据...

-- 4. 更新主账号微信信息
UPDATE users 
SET openid = (SELECT openid FROM users WHERE id = B),
    unionid = (SELECT unionid FROM users WHERE id = B),
    wechat_nickname = (SELECT wechat_nickname FROM users WHERE id = B),
    wechat_avatar = (SELECT wechat_avatar FROM users WHERE id = B)
WHERE id = A;

-- 5. 标记旧账号为已合并
UPDATE users SET status = 9, merged_to_user_id = A WHERE id = B;
```

## 三、API接口设计

### 3.1 微信静默登录接口

```php
/**
 * 微信静默登录
 * POST /api/user/auth/wechat/silent-login
 */
public function wechatSilentLogin(Request $request)
{
    // 参数：code, platform
    // 返回：token或需要授权登录的提示
}
```

### 3.2 微信授权登录接口

```php
/**
 * 微信授权登录
 * POST /api/user/auth/wechat/authorized-login
 */
public function wechatAuthorizedLogin(Request $request)
{
    // 参数：code, userInfo, platform
    // 返回：token或需要绑定手机号的提示
}
```

### 3.3 绑定手机号接口

```php
/**
 * 微信用户绑定手机号
 * POST /api/user/auth/wechat/bind-phone
 */
public function bindPhone(Request $request)
{
    // 参数：openid, phone, code(短信验证码)
    // 返回：绑定结果和token
}
```

### 3.4 账号合并接口

```php
/**
 * 账号合并确认
 * POST /api/user/auth/merge-account
 */
public function mergeAccount(Request $request)
{
    // 参数：phone, password, openid
    // 返回：合并结果
}
```

## 四、业务流程设计

### 4.1 微信静默登录流程

```mermaid
flowchart TD
    A[用户打开小程序] --> B[调用wx.login获取code]
    B --> C[发送code到后端]
    C --> D[后端调用微信API获取openid]
    D --> E{数据库中是否存在该openid用户?}
    E -->|存在| F[直接登录成功，返回token]
    E -->|不存在| G[返回需要授权登录的提示]
    F --> H[登录完成]
    G --> I[引导用户进行授权登录]
```

### 4.2 微信授权登录流程

```mermaid
flowchart TD
    A[用户点击授权登录] --> B[调用wx.getUserProfile]
    B --> C[获取用户信息和code]
    C --> D[发送到后端]
    D --> E[获取openid和unionid]
    E --> F{根据unionid查找用户}
    F -->|找到用户| G[直接登录]
    F -->|未找到| H{是否有手机号?}
    H -->|有| I[创建账号并登录]
    H -->|无| J[要求绑定手机号]
    G --> K[登录完成]
    I --> K
    J --> L[用户输入手机号验证码]
    L --> M[绑定手机号并创建账号]
    M --> K
```

### 4.3 账号合并流程

```mermaid
flowchart TD
    A[检测到需要合并的账号] --> B[提示用户存在多个账号]
    B --> C[用户选择合并方式]
    C --> D{选择主账号}
    D -->|手机号账号为主| E[迁移微信账号数据]
    D -->|微信账号为主| F[绑定手机号到微信账号]
    E --> G[更新微信信息到手机号账号]
    F --> H[验证手机号并绑定]
    G --> I[标记旧账号为已合并]
    H --> J[账号合并完成]
    I --> J
```

## 五、实现细节

### 5.1 微信API集成

#### 5.1.1 小程序登录
```php
// 获取session_key和openid
$url = "https://api.weixin.qq.com/sns/jscode2session";
$params = [
    'appid' => config('wechat.miniprogram.app_id'),
    'secret' => config('wechat.miniprogram.secret'),
    'js_code' => $code,
    'grant_type' => 'authorization_code'
];
```

#### 5.1.2 APP微信登录
```php
// APP微信登录需要不同的处理方式
// 需要配置APP的AppID和Secret
```

### 5.2 用户匹配逻辑

```php
/**
 * 用户匹配和合并逻辑
 */
private function findOrCreateUser($wechatData, $platform)
{
    // 1. 优先通过UnionID查找
    if (!empty($wechatData['unionid'])) {
        $user = User::where('unionid', $wechatData['unionid'])->first();
        if ($user) {
            return $this->updateUserWechatInfo($user, $wechatData, $platform);
        }
    }
    
    // 2. 通过OpenID查找
    $user = User::where('openid', $wechatData['openid'])->first();
    if ($user) {
        return $this->updateUserWechatInfo($user, $wechatData, $platform);
    }
    
    // 3. 如果都没找到，创建新用户（但需要绑定手机号才能完成注册）
    return $this->createWechatUser($wechatData, $platform);
}
```

### 5.3 安全考虑

1. **数据验证**: 严格验证微信返回的数据
2. **重放攻击防护**: 限制code的使用次数和时间
3. **用户数据保护**: 敏感信息加密存储
4. **访问控制**: 合并操作需要额外验证

## 六、配置文件修改

### 6.1 微信配置 (config/wechat.php)

```php
<?php

return [
    // 小程序配置
    'miniprogram' => [
        'app_id' => env('WECHAT_MINIPROGRAM_APPID'),
        'secret' => env('WECHAT_MINIPROGRAM_SECRET'),
    ],
    
    // APP配置
    'app' => [
        'app_id' => env('WECHAT_APP_APPID'),
        'secret' => env('WECHAT_APP_SECRET'),
    ],
    
    // H5配置
    'h5' => [
        'app_id' => env('WECHAT_H5_APPID'),
        'secret' => env('WECHAT_H5_SECRET'),
    ],
    
    // 通用配置
    'timeout' => 5.0,
    'retry' => 3,
];
```

### 6.2 环境变量 (.env)

```env
# 微信小程序
WECHAT_MINIPROGRAM_APPID=your_miniprogram_appid
WECHAT_MINIPROGRAM_SECRET=your_miniprogram_secret

# 微信APP
WECHAT_APP_APPID=your_app_appid
WECHAT_APP_SECRET=your_app_secret

# 微信H5
WECHAT_H5_APPID=your_h5_appid
WECHAT_H5_SECRET=your_h5_secret
```

## 七、测试计划

### 7.1 单元测试

1. 微信API调用测试
2. 用户匹配逻辑测试
3. 账号合并逻辑测试
4. 数据迁移测试

### 7.2 集成测试

1. 完整登录流程测试
2. 多平台兼容性测试
3. 并发登录测试
4. 异常情况处理测试

### 7.3 用户验收测试

1. 新用户微信注册流程
2. 老用户微信绑定流程
3. 账号合并用户体验
4. 跨平台登录体验

## 八、实施计划

### 第一阶段：基础功能开发
1. 数据库表结构调整
2. 微信API集成
3. 基础登录接口开发

### 第二阶段：用户合并功能
1. 用户匹配逻辑实现
2. 账号合并接口开发
3. 数据迁移脚本编写

### 第三阶段：测试和优化
1. 全面测试
2. 性能优化
3. 安全加固

### 第四阶段：上线部署
1. 生产环境配置
2. 灰度发布
3. 全量上线

## 九、风险评估与对策

### 9.1 主要风险

1. **数据丢失风险**: 账号合并过程中可能出现数据丢失
   - **对策**: 完善的备份机制和回滚方案

2. **用户体验风险**: 复杂的账号合并流程可能影响用户体验
   - **对策**: 简化流程，提供清晰的引导

3. **性能风险**: 大量用户同时进行账号合并可能影响系统性能
   - **对策**: 异步处理和分批迁移

4. **安全风险**: 微信登录可能带来新的安全隐患
   - **对策**: 严格的数据验证和访问控制

### 9.2 回滚方案

1. 保留原有登录方式作为备选
2. 数据库变更支持回滚
3. 配置开关控制新功能启用

## 十、监控和维护

### 10.1 监控指标

1. 微信登录成功率
2. 账号合并成功率
3. API响应时间
4. 错误日志统计

### 10.2 日常维护

1. 定期清理登录日志
2. 监控异常登录行为
3. 更新微信API版本
4. 用户反馈处理

## 总结

本方案提供了完整的微信登录集成和用户兼容解决方案，以手机号为用户唯一标识，支持多平台用户账号合并，确保用户数据的完整性和一致性。通过分阶段实施，可以最大程度降低风险，提升用户体验。 
