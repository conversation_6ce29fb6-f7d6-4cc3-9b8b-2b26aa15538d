<?php

use App\Http\Controllers\Api\User\AuthController as UserAuthController;
use App\Http\Controllers\Api\User\ServiceController as UserServiceController;
use App\Http\Controllers\Api\User\OrderController as UserOrderController;
use App\Http\Controllers\Api\User\MaterialOrderController as UserMaterialOrderController;
use App\Http\Controllers\Api\User\Material\MaterialController as UserMaterialController;
use App\Http\Controllers\Api\User\Material\CategoryController as UserMaterialCategoryController;
use App\Http\Controllers\Api\User\Material\OrderController as UserMatOrderController;
use App\Http\Controllers\Api\Worker\AuthController as WorkerAuthController;
use App\Http\Controllers\Api\Worker\OrderController as WorkerOrderController;
use App\Http\Controllers\Api\Worker\MaterialOrderController as WorkerMaterialOrderController;
use App\Http\Controllers\Api\Worker\Material\MaterialController as WorkerMaterialController;
use App\Http\Controllers\Api\Worker\Material\CategoryController as WorkerMaterialCategoryController;
use App\Http\Controllers\Api\Worker\Material\CartController as WorkerMaterialCartController;
use App\Http\Controllers\Api\Worker\Material\OrderController as WorkerMatOrderController;
use App\Http\Controllers\Api\Worker\BankCardController;
use App\Http\Controllers\Api\Worker\DepositController;
use App\Http\Controllers\Api\Worker\LevelController;
use App\Http\Controllers\Api\Worker\QuitController;
use App\Http\Controllers\Api\Worker\ReimbursementController;
use App\Http\Controllers\Api\Worker\Income\IncomeController;
use App\Http\Controllers\Api\Worker\Income\WithdrawalController;
use App\Http\Controllers\Api\Client\ArticleController;
use App\Http\Controllers\Api\Client\ContentController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 基本设置接口
Route::get('setting', [ContentController::class, 'setting']);
// 违约费用设置接口
Route::get('cancellation-fees', [\App\Http\Controllers\Api\Client\SettingController::class, 'cancellationFees']);
// 首页数据接口
Route::get('index', [\App\Http\Controllers\Api\Client\IndexController::class, 'index']);

// 用户端API路由
Route::prefix('user')->group(function () {
    // 公开路由
    Route::post('login', [UserAuthController::class, 'login']);
    Route::post('register', [UserAuthController::class, 'register']);
    Route::post('wx-login', [UserAuthController::class, 'wxLogin']);

    // 微信登录三步流程接口
    Route::post('wechat/silent-login', [UserAuthController::class, 'wechatSilentLogin']);
    Route::post('wechat/authorized-login', [UserAuthController::class, 'wechatAuthorizedLogin']);
    Route::post('wechat/bind-phone', [UserAuthController::class, 'wechatBindPhone']);
    Route::post('wechat/update-profile', [UserAuthController::class, 'wechatUpdateProfile']);

    Route::post('verify-code', [UserAuthController::class, 'sendVerifyCode']);
    Route::post('reset-password', [UserAuthController::class, 'resetPassword']);
    Route::get('areas', [ContentController::class, 'area']);

        // 需要认证的路由
    Route::middleware('user.auth')->group(function () {
        Route::post('logout', [UserAuthController::class, 'logout']);
        Route::post('refresh', [UserAuthController::class, 'refresh']);
        Route::get('me', [UserAuthController::class, 'me']);
        Route::post('change-password', [UserAuthController::class, 'changePassword']);
        Route::post('update-profile', [UserAuthController::class, 'updateProfile']);

        // 其他需要用户认证的路由
    });

    // 材料管理模块 - 用户端
    Route::prefix('materials')->group(function () {
        // 获取材料列表
        Route::get('/', [UserMaterialController::class, 'index']);
        // 搜索材料
        Route::get('/search', [UserMaterialController::class, 'search']);
        // 获取材料详情
        Route::get('/{material}', [UserMaterialController::class, 'show']);
    });

    // 材料分类 - 用户端
    Route::get('/material/categories', [UserMaterialCategoryController::class, 'index']);

    // 材料订单 - 用户端
    Route::middleware('user.auth')->prefix('material/orders')->group(function () {
        // 获取材料订单列表
        Route::get('/', [UserMatOrderController::class, 'index']);
        // 获取材料订单详情
        Route::get('/{order}', [UserMatOrderController::class, 'show']);
        // 确认材料使用
        Route::post('/{order}/confirm-usage', [UserMatOrderController::class, 'confirmUsage']);
        // 修改材料订单（仅未支付状态可修改）
        Route::put('/{id}', [UserMaterialOrderController::class, 'update']);
    });

    // 支付模块
    Route::middleware('user.auth')->prefix('payments')->group(function () {
        Route::get('/prepay', [App\Http\Controllers\Api\User\PaymentController::class, 'prepay']);
        Route::post('/create', [App\Http\Controllers\Api\User\PaymentController::class, 'create']);
        Route::get('/status', [App\Http\Controllers\Api\User\PaymentController::class, 'status']);
        Route::get('/records', [App\Http\Controllers\Api\User\PaymentController::class, 'records']);
    });

    // 退款模块
    Route::prefix('refunds')->group(function () {
        Route::post('/apply', [App\Http\Controllers\Api\User\RefundController::class, 'apply']);
        Route::get('/records', [App\Http\Controllers\Api\User\RefundController::class, 'records']);
        // 退款回调（不需要用户认证）
        Route::post('/callback', [App\Http\Controllers\Api\User\RefundController::class, 'callback'])->withoutMiddleware(['user.auth']);
    });

    // 地址管理
    Route::get('addresses', [App\Http\Controllers\Api\User\UserAddressController::class, 'index']);
    Route::get('addresses/{id}', [App\Http\Controllers\Api\User\UserAddressController::class, 'show']);
    Route::post('addresses', [App\Http\Controllers\Api\User\UserAddressController::class, 'store']);
    Route::put('addresses/{id}', [App\Http\Controllers\Api\User\UserAddressController::class, 'update']);
    Route::delete('addresses/{id}', [App\Http\Controllers\Api\User\UserAddressController::class, 'destroy']);
    Route::post('addresses/{id}/default', [App\Http\Controllers\Api\User\UserAddressController::class, 'setDefault']);

    // 用户充值相关
    Route::middleware('auth:user')->group(function () {
        Route::post('/recharge', [\App\Http\Controllers\Api\User\RechargeController::class, 'recharge']);
        Route::get('/recharge-records', [\App\Http\Controllers\Api\User\RechargeController::class, 'rechargeRecords']);
        Route::get('/recharge-activities-list', [\App\Http\Controllers\Api\User\RechargeController::class, 'getRechargeActivities']);
        Route::get('/wallet-transactions', [\App\Http\Controllers\Api\User\RechargeController::class, 'walletTransactions']);
        Route::get('/wallet-info', [\App\Http\Controllers\Api\User\RechargeController::class, 'walletInfo']);
    });

    // 用户消费记录相关
    Route::middleware('auth:user')->prefix('consumption')->group(function () {
        // 获取消费记录列表
        Route::get('/records', [\App\Http\Controllers\Api\User\ConsumptionController::class, 'records']);
        // 获取消费记录详情
        Route::get('/detail/{id}', [\App\Http\Controllers\Api\User\ConsumptionController::class, 'detail']);
        // 获取消费统计信息
        Route::get('/statistics', [\App\Http\Controllers\Api\User\ConsumptionController::class, 'statistics']);
    });
});

// 添加服务收藏相关的路由
Route::middleware('user.auth')->prefix('services')->group(function () {
    // 添加收藏
    Route::post('/favorite', [UserServiceController::class, 'favorite']);
    // 取消收藏
    Route::post('/unfavorite', [UserServiceController::class, 'unfavorite']);
    // 获取收藏列表
    Route::get('/favorites', [UserServiceController::class, 'favorites']);
    // 检查是否已收藏
    Route::get('/is-favorite', [UserServiceController::class, 'isFavorite']);
});

// 服务管理模块API路由（用户端）
Route::prefix('services')->group(function () {
    // 获取服务列表
    Route::get('/', [UserServiceController::class, 'index']);
    // 获取热门服务
    Route::get('/hot', [UserServiceController::class, 'hotServices']);
    // 搜索服务
    Route::get('/search', [UserServiceController::class, 'search']);
    // 获取服务详情
    Route::get('/{id}', [UserServiceController::class, 'show']);
    // 获取服务评价
    Route::get('/{id}/reviews', [UserServiceController::class, 'reviews']);
});



// 获取服务分类（用户端）
Route::get('/service-categories', [App\Http\Controllers\Api\Client\ServiceCategoryController::class, 'index']);
Route::get('/service-categories/{id}', [App\Http\Controllers\Api\Client\ServiceCategoryController::class, 'show']);
Route::get('/service-categories/{id}/services', [App\Http\Controllers\Api\Client\ServiceCategoryController::class, 'services']);

// 公开的订单大厅（无需登录）
Route::get('/orders/public-hall', [WorkerOrderController::class, 'publicHall']);

// 订单管理模块API路由（用户端）
Route::middleware('user.auth')->group(function () {
    // 订单相关
    Route::prefix('orders')->group(function () {
        // 预支付计算
        Route::post('/pre-calculate', [UserOrderController::class, 'preCalculate']);
        // 创建订单
        Route::post('/', [UserOrderController::class, 'store']);
        // 获取订单列表
        Route::get('/reviews', [UserOrderController::class, 'reviewList']);
        Route::get('/', [UserOrderController::class, 'index']);
        // 获取订单详情
        Route::get('/{id}', [UserOrderController::class, 'show']);
        // 取消订单
        Route::post('/{id}/cancel', [UserOrderController::class, 'cancel']);
        // 支付预约费用
        Route::post('/{id}/pay-booking', [UserOrderController::class, 'payBookingFee']);
        // 支付服务费用
        Route::post('/{id}/pay-service', [UserOrderController::class, 'payServiceFee']);
        // 确认维修完成
        Route::post('/{id}/complete', [UserOrderController::class, 'complete']);
        // 提交评价
        Route::post('/{id}/review', [UserOrderController::class, 'review']);
        // 获取订单状态流转记录
        Route::get('/{id}/status-logs', [UserOrderController::class, 'statusLogs']);
        // 获取材料订单列表
        Route::get('/{id}/material-orders', [UserMaterialOrderController::class, 'index']);
        // 删除订单
        Route::delete('/{id}', [UserOrderController::class, 'destroy']);
        // 获取评论列表
    });

    // 材料订单相关
    Route::prefix('material-orders')->group(function () {
        // 获取材料订单详情
        Route::get('/{id}', [UserMaterialOrderController::class, 'show']);
        // 确认材料使用
        Route::post('/{id}/confirm-usage', [UserMaterialOrderController::class, 'confirmUsage']);
        // 支付材料订单
        Route::post('/{id}/pay', [UserMaterialOrderController::class, 'pay']);
        // 修改材料订单（仅未支付状态可修改）
        Route::put('/{id}', [UserMaterialOrderController::class, 'update']);
    });
});

// 师傅端API路由
Route::prefix('worker')->group(function () {
    // 公开路由
    Route::post('login', [WorkerAuthController::class, 'login']);
    Route::post('register', [WorkerAuthController::class, 'register']);
    Route::post('wx-login', [WorkerAuthController::class, 'wxLogin']);
    Route::post('verify-code', [WorkerAuthController::class, 'sendVerifyCode']);
    Route::post('reset-password', [WorkerAuthController::class, 'resetPassword']);

    // 微信登录相关路由
    Route::prefix('wechat')->group(function () {
        Route::post('silent-login', [WorkerAuthController::class, 'wechatSilentLogin']);
        Route::post('authorized-login', [WorkerAuthController::class, 'wechatAuthorizedLogin']);
        Route::post('bind-register', [WorkerAuthController::class, 'wechatBindRegister']);
        // 微信绑定手机号（需要认证）
        Route::post('bind-phone', [WorkerAuthController::class, 'wechatBindPhone']);
    });

    // 需要认证的路由
    Route::middleware('worker.auth')->group(function () {
        Route::post('logout', [WorkerAuthController::class, 'logout']);
        Route::post('refresh', [WorkerAuthController::class, 'refresh']);
        Route::get('me', [WorkerAuthController::class, 'me']);
        Route::post('change-password', [WorkerAuthController::class, 'changePassword']);
        Route::post('update-profile', [WorkerAuthController::class, 'updateProfile']);


        // 订单相关路由
        Route::prefix('orders')->group(function () {
            // 获取订单列表 材料订单下单时 返回对应状态的订单
            Route::get('/get-order-by-pre-material-order', [WorkerOrderController::class, 'getOrderByPreMaterialOrder']);
            // 获取订单列表
            Route::get('/', [WorkerOrderController::class, 'index']);
            // 获取订单详情
            Route::get('/{id}', [WorkerOrderController::class, 'show']);
            // 师傅接单
            Route::post('/{id}/accept', [WorkerOrderController::class, 'accept']);
            // 师傅到达确认
            Route::post('/{id}/arrive', [WorkerOrderController::class, 'arrive']);
            // 设定服务费用
            Route::post('/{id}/set-service-fee', [WorkerOrderController::class, 'setServiceFee']);
            // 上传施工图片
            Route::post('/{id}/construction-images', [WorkerOrderController::class, 'uploadConstructionImages']);
            // 上传材料使用图片
            Route::post('/{id}/material-images', [WorkerOrderController::class, 'uploadMaterialImages']);
            // 上传完工图片
            Route::post('/{id}/completion-images', [WorkerOrderController::class, 'uploadCompletionImages']);
            // 订单核销
            Route::post('/{id}/verify', [WorkerOrderController::class, 'verify']);
            // 获取材料订单列表
            Route::get('/{id}/material-orders', [WorkerMaterialOrderController::class, 'index']);
            // 核销订单（维修完成）
            Route::post('/{id}/verify-service', [WorkerOrderController::class, 'verifyService']);
            // 取消订单
            Route::post('/{id}/cancel', [WorkerOrderController::class, 'cancelOrder']);
        });

        // 订单大厅
        Route::get('/order/hall', [WorkerOrderController::class, 'hall']);

        // 材料订单相关
        Route::prefix('material-orders')->group(function () {

            // 获取订单数量统计
            Route::get('/count', [WorkerMaterialOrderController::class, 'count']);
            // 创建材料订单
            Route::post('/', [WorkerMaterialOrderController::class, 'store']);
            // 获取材料订单详情
            Route::get('/{id}', [WorkerMaterialOrderController::class, 'show']);
            // 上传材料使用图片
            Route::post('/{id}/usage-images', [WorkerMaterialOrderController::class, 'uploadUsageImages']);
            // 修改材料订单（仅未支付状态可修改）
            Route::put('/{id}', [WorkerMaterialOrderController::class, 'update']);
        });

        // 材料管理模块 - 师傅端
        Route::prefix('materials')->group(function () {
            Route::get('/favorites', [WorkerMaterialController::class, 'favorites']);
            // 获取材料列表
            Route::get('/', [WorkerMaterialController::class, 'index']);
            // 获取材料详情
            Route::get('/{material}', [WorkerMaterialController::class, 'show']);
            // 添加收藏
            Route::post('/{material}/favorite', [WorkerMaterialController::class, 'addFavorite']);
            // 取消收藏
            Route::delete('/{material}/favorite', [WorkerMaterialController::class, 'removeFavorite']);
            // 收藏列表
        });

        // 材料分类 - 师傅端
        Route::get('/material/categories', [WorkerMaterialCategoryController::class, 'index']);

        // 购物车 - 师傅端
        Route::prefix('cart')->group(function () {
            // 获取购物车列表
            Route::get('/', [WorkerMaterialCartController::class, 'index']);
            // 添加购物车
            Route::post('/add', [WorkerMaterialCartController::class, 'add']);
            // 更新购物车
            Route::post('/update', [WorkerMaterialCartController::class, 'update']);
            // 删除购物车项
            Route::post('/delete', [WorkerMaterialCartController::class, 'delete']);
            // 选中删除
            Route::post('/delete-selected', [WorkerMaterialCartController::class, 'deleteSelected']);
            // 清空购物车
            Route::post('/clear', [WorkerMaterialCartController::class, 'clear']);
        });

        // 材料订单 - 师傅端
        Route::prefix('material/orders')->group(function () {
            // 获取材料订单列表
            Route::get('/', [WorkerMatOrderController::class, 'index']);
            // 创建材料订单
            Route::post('/', [WorkerMatOrderController::class, 'store']);
            // 获取材料订单详情
            Route::get('/{order}', [WorkerMatOrderController::class, 'show']);
            // 上传材料使用图片
            Route::post('/{order}/upload-usage-images', [WorkerMatOrderController::class, 'uploadUsageImages']);
        });

        // 师傅等级相关
        Route::get('/level-info', [LevelController::class, 'info']);
        Route::get('/level-upgrade-history', [LevelController::class, 'upgradeHistory']);

        // 收益明细相关
        Route::prefix('income')->group(function () {
            Route::get('/', [IncomeController::class, 'index']);
            Route::get('/overview', [IncomeController::class, 'overview']);
            Route::get('/records', [IncomeController::class, 'index']);
            Route::post('/withdraw', [WithdrawalController::class, 'withdraw']);
            Route::get('/withdrawal-records', [WithdrawalController::class, 'records']);
        });

        // 技能相关
        Route::prefix('skills')->group(function () {
            Route::get('/available', [App\Http\Controllers\Api\Worker\SkillController::class, 'available']);
            Route::post('/apply', [App\Http\Controllers\Api\Worker\SkillController::class, 'apply']);
            Route::get('/my', [App\Http\Controllers\Api\Worker\SkillController::class, 'mySkills']);
        });

        Route::get('withdraws/', [WithdrawalController::class, 'index']);
        // 提现申请相关
        Route::prefix('withdraw')->group(function () {
            Route::post('/', [WithdrawalController::class, 'store']);
        });

        // 银行信息
        Route::prefix('banks')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\Worker\BankController::class, 'index']);
            Route::get('/{id}', [App\Http\Controllers\Api\Worker\BankController::class, 'show']);
        });

        // 银行卡管理
        Route::prefix('bank-cards')->group(function () {
            Route::get('/', [BankCardController::class, 'index']);
            Route::post('/', [BankCardController::class, 'store']);
            Route::delete('/{id}', [BankCardController::class, 'destroy']);
        });

        // 押金管理
        Route::get('/deposit-info', [DepositController::class, 'info']);

        // 取消入驻申请
        Route::prefix('quit')->group(function () {
            Route::post('/', [QuitController::class, 'apply']);
            Route::get('/status', [QuitController::class, 'status']);
        });

        Route::post('reimbursement', [ReimbursementController::class, 'store']);
        // 报销管理
        Route::prefix('reimbursements')->group(function () {
            Route::get('/', [ReimbursementController::class, 'index']);
            Route::post('/upload-image', [ReimbursementController::class, 'uploadImage']);
        });

        // 接单状态设置
        Route::post('/working-status', [WorkerAuthController::class, 'setWorkingStatus']);
    });
});

// 内容管理模块API路由（客户端）
Route::prefix('client')->group(function () {
    // 公开路由 - 不需要认证
    // 文件上传相关
    Route::post('upload/image', [App\Http\Controllers\Api\Client\UploadController::class, 'uploadImage']);
    Route::post('upload/file', [App\Http\Controllers\Api\Client\UploadController::class, 'uploadFile']);
    Route::post('upload/delete', [App\Http\Controllers\Api\Client\UploadController::class, 'deleteFile']);

    // 推荐内容相关
    Route::prefix('recommendations')->group(function () {
        Route::get('/services', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedServices']);
        Route::get('/courses', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedCourses']);
        Route::get('/jobs', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedJobs']);
        Route::get('/all', [App\Http\Controllers\Api\Client\RecommendationController::class, 'allRecommendations']);
    });

    // 招聘信息相关
    Route::get('jobs', 'App\Http\Controllers\Api\Client\JobController@index');
    Route::get('jobs/education-options', 'App\Http\Controllers\Api\Client\JobController@educationOptions');
    Route::get('jobs/areas', 'App\Http\Controllers\Api\Client\JobController@areas');
    Route::get('jobs/company-scales', 'App\Http\Controllers\Api\Client\JobController@companyScales');
    Route::get('jobs/company-industries', 'App\Http\Controllers\Api\Client\JobController@companyIndustries');
    Route::get('jobs/{id}', 'App\Http\Controllers\Api\Client\JobController@show');

    // 课程分类相关
    Route::get('/course-categories', [App\Http\Controllers\Api\Client\CourseCategoryController::class, 'index']);
    Route::get('/course-categories/{id}', [App\Http\Controllers\Api\Client\CourseCategoryController::class, 'show']);
    Route::get('course-categories', 'App\Http\Controllers\Api\Client\CourseCategoryController@index');
    Route::get('course-categories/{id}', 'App\Http\Controllers\Api\Client\CourseCategoryController@show');
    Route::get('course-categories/{id}/courses', 'App\Http\Controllers\Api\Client\CourseCategoryController@courses');

    // 课程目录相关
    Route::get('course-directories/main-courses', 'App\Http\Controllers\Api\Client\CourseDirectoryController@getMainCourseDirectory');
    Route::get('course-directories/exam-subjects', 'App\Http\Controllers\Api\Client\CourseDirectoryController@getExamSubjectDirectory');
    Route::get('course-directories/courses', 'App\Http\Controllers\Api\Client\CourseDirectoryController@getDirectoryCourses');

    // 课程学习相关 - 公开部分
    Route::get('courses', 'App\Http\Controllers\Api\Client\CourseController@index');
    Route::get('courses/{id}', 'App\Http\Controllers\Api\Client\CourseController@show');
    Route::get('course-tags', 'App\Http\Controllers\Api\Client\CourseController@tags');

    // 首页内容相关
    Route::get('banners', 'App\Http\Controllers\Api\Client\ContentController@banners');
    Route::get('nav-buttons', 'App\Http\Controllers\Api\Client\ContentController@navButtons');

    // 基础数据
    Route::get('main-courses', 'App\Http\Controllers\Api\Client\BasicDataController@mainCourses');
    Route::get('exam-subjects', 'App\Http\Controllers\Api\Client\BasicDataController@examSubjects');

    // 需要用户认证的路由
    Route::middleware('user.auth')->group(function () {
        // 招聘信息 - 需要认证的部分
        Route::post('job-applications', 'App\Http\Controllers\Api\Client\JobApplicationController@store');
        Route::get('job-applications', 'App\Http\Controllers\Api\Client\JobApplicationController@index');

        // 课程学习 - 需要认证的部分
        Route::get('courses/{id}/view', 'App\Http\Controllers\Api\Client\CourseController@view');
        Route::post('courses/{id}/progress', 'App\Http\Controllers\Api\Client\CourseController@saveProgress');
        Route::post('courses/{id}/favorite', 'App\Http\Controllers\Api\Client\CourseController@toggleFavorite');
        Route::post('courses/{id}/apply-permission', 'App\Http\Controllers\Api\Client\CourseController@applyPermission');

        // 个人中心相关
        Route::get('my-favorite-courses', 'App\Http\Controllers\Api\Client\UserCenterController@favoriteCourses');
        Route::get('my-learning-records', 'App\Http\Controllers\Api\Client\UserCenterController@learningRecords');
    });

    // 获取技能列表
    Route::get('skills', [App\Http\Controllers\Api\Client\SkillController::class, 'index']);
    // 获取技能分类
    Route::get('skill-categories', [App\Http\Controllers\Api\Client\SkillController::class, 'categories']);
    // 获取技能详情
    Route::get('skills/{id}', [App\Http\Controllers\Api\Client\SkillController::class, 'show']);
    // 按分类获取技能
    Route::get('skill-categories/{categoryId}/skills', [App\Http\Controllers\Api\Client\SkillController::class, 'getByCategory']);

    // 预约时间段API
    Route::get('appointment/time-slots', 'App\Http\Controllers\Api\Client\AppointmentController@getTimeSlots');
    Route::get('appointment/time-slots/date', 'App\Http\Controllers\Api\Client\AppointmentController@getTimeSlotsForDate');

    // IP定位API
    Route::get('ip/location', 'App\Http\Controllers\Api\Client\IpLocationController@locate');

    // 内容相关API
    Route::get('/recommendations/services', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedServices']);
    Route::get('/recommendations/courses', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedCourses']);
    Route::get('/recommendations/jobs', [App\Http\Controllers\Api\Client\RecommendationController::class, 'recommendedJobs']);
    Route::get('/recommendations/all', [App\Http\Controllers\Api\Client\RecommendationController::class, 'allRecommendations']);
    Route::get('/hot-jobs', [App\Http\Controllers\Api\Client\RecommendationController::class, 'hotJobs']);
    Route::get('/hot-courses', [App\Http\Controllers\Api\Client\RecommendationController::class, 'hotCourses']);
});

// 营销管理模块API路由

// 充值活动相关路由
Route::get('/recharge-activities', [\App\Http\Controllers\Api\Marketing\RechargeActivityController::class, 'index']);
Route::get('/recharge-activities/{rechargeActivity}', [\App\Http\Controllers\Api\Marketing\RechargeActivityController::class, 'show']);

// 优惠券相关路由
Route::get('/coupons', [\App\Http\Controllers\Api\Marketing\CouponController::class, 'index']);
Route::get('/coupons/{coupon}', [\App\Http\Controllers\Api\Marketing\CouponController::class, 'show']);
Route::post('/coupons/{coupon}/issue', [\App\Http\Controllers\Api\Marketing\CouponController::class, 'issue']);

// 年费套餐相关路由
Route::get('/annual-packages', [\App\Http\Controllers\Api\Marketing\AnnualPackageController::class, 'index']);
Route::get('/annual-packages/{annualPackage}', [\App\Http\Controllers\Api\Marketing\AnnualPackageController::class, 'show']);

// 用户端营销模块路由
Route::prefix('user')->middleware('user.auth')->group(function () {
    // 用户优惠券相关
    Route::get('/coupons', [\App\Http\Controllers\Api\User\CouponController::class, 'index']);
    Route::post('/coupons/receive', [\App\Http\Controllers\Api\User\CouponController::class, 'receive']);
    Route::post('/orders/{orderId}/apply-coupon', [\App\Http\Controllers\Api\User\CouponController::class, 'applyCoupon']);
});

// 师傅端营销模块路由
Route::prefix('worker')->middleware('worker.auth')->group(function () {
    // 年费相关
    Route::get('/annual-package-purchases', [\App\Http\Controllers\Api\Worker\AnnualPackageController::class, 'purchaseRecords']);
    Route::post('/annual-packages/{id}/purchase', [\App\Http\Controllers\Api\Worker\AnnualPackageController::class, 'purchase']);
    Route::get('/annual-fee-status', [\App\Http\Controllers\Api\Worker\AnnualPackageController::class, 'checkStatus']);
    Route::get('/annual-fee-payment-status', [\App\Http\Controllers\Api\Worker\AnnualPackageController::class, 'status']);
});

// 模拟支付回调路由（实际项目中需要根据支付网关调整）
Route::get('/mock-payment/{orderNo}', function($orderNo) {
    if (strpos($orderNo, 'R') === 0) {
        return (new \App\Http\Controllers\Api\User\RechargeController())->paymentCallback($orderNo);
    } elseif (strpos($orderNo, 'A') === 0) {
        return (new \App\Http\Controllers\Api\Worker\AnnualPackageController())->paymentCallback($orderNo);
    }

    return response()->json([
        'code' => 1,
        'message' => '未知订单类型',
    ], 400);
});

// 支付回调（不需要认证）
Route::post('/user/payments/callback', [App\Http\Controllers\Api\User\PaymentController::class, 'callback'])
    ->name('wechat.payments.callback')
    ->middleware('wechat.payment.callback');

// 支付宝同步回调（不需要认证）
Route::get('/payments/alipay/return', [App\Http\Controllers\Api\User\PaymentController::class, 'alipayReturn'])
    ->name('api.payments.alipay.return');

// 文章相关接口
Route::prefix('articles')->group(function () {
    // 获取文章分类列表
    Route::get('categories', [\App\Http\Controllers\Api\ArticleController::class, 'categories']);
    // 获取文章列表
    Route::get('/', [\App\Http\Controllers\Api\ArticleController::class, 'index']);
    // 获取文章详情
    Route::get('/{id}', [\App\Http\Controllers\Api\ArticleController::class, 'show']);
});

// 单页内容路由
Route::get('/pages/{slug}', [App\Http\Controllers\Api\Client\PageController::class, 'show']);

