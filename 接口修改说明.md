# 接口修改说明

## 修改概述

本次修改主要针对用户端（UserOrderController）和师傅端（WorkerOrderController）的订单控制器进行了功能完善和调整，增强了系统的可用性和用户体验。

## 一、用户端订单控制器（OrderController.php）修改

### 1. 订单状态文本优化

- 优化了订单状态的展示逻辑，提供了简化版的状态文本（getSimplifiedStatusText方法）
- 状态展示分为两种：simple_status（简化状态）和 detail_status（详细状态）
- 简化状态主要用于列表展示，将多个流程中状态合并为"待服务"，便于用户理解

### 2. 支付相关功能完善

- 完善了预约费支付（payBookingFee）方法，支持多种支付方式
- 完善了服务费支付（payServiceFee）方法，增加了全额/定金支付选项
- 增加了支付状态查询和校验逻辑，确保支付流程稳定可靠

### 3. 订单详情功能增强

- 详情接口返回数据结构优化，增加了更多状态显示
- 新增了各种业务按钮的显示控制字段：
  - `show_confirm_material_used_button`: 是否显示确认材料使用按钮
  - `show_pay_material_fee_button`: 是否显示支付材料费按钮
  - `show_pay_service_fee_button`: 是否显示支付服务费按钮
  - `show_confirm_repair_completed_button`: 是否显示确认维修完成按钮
- 增加了核销码的二维码base64信息，方便前端直接展示

### 4. 评价功能完善

- 完善了评价提交逻辑，增加图片上传支持
- 优化了评价数据的返回结构，方便前端展示

### 5. 订单取消与退款

- 增强了订单取消功能，增加了取消原因字段
- 根据取消时间与预约时间的差距计算退款金额
- 支持多种支付方式的退款处理

### 6. 新增功能

- 增加了获取订单状态流转记录的接口
- 增加了删除订单的接口（限制仅可删除已完成或已取消的订单）
- 新增了二维码生成功能，支持核销码的图形化展示

## 二、师傅端订单控制器（WorkerOrderController.php）修改

### 1. 订单列表优化

- 改进了订单列表查询逻辑，按照待接单、待服务、待确认、已完成等类型进行分组
- 提供了各状态的订单数量统计，方便师傅了解工作量
- 增加了各种按钮的显示控制字段，例如：
  - `show_set_service_fee_button`: 是否显示设定服务费按钮
  - `show_change_service_fee_button`: 是否显示修改服务费按钮
  - `show_verify_button`: 是否显示扫码核销按钮
  - `show_select_material_button`: 是否显示选购材料按钮
  - `show_upload_construction_image_button`: 是否显示上传施工图片按钮
  - `show_upload_completion_image_button`: 是否显示上传完工图片按钮

### 2. 图片上传功能

- 新增了三种图片上传功能：
  - 施工过程图片上传（uploadConstructionImages方法）
  - 材料使用图片上传（uploadMaterialImages方法）
  - 完工图片上传（uploadCompletionImages方法）
- 每种图片类型均有数量限制（最多6张），避免过多图片影响系统性能

### 3. 服务费用设定优化

- 完善了服务费用设定功能，支持定金模式
- 增加了费用说明字段，方便记录费用产生的原因

### 4. 订单核销流程优化

- 通过验证码进行订单核销，确保服务真实完成
- 增加了到达确认和服务完成两个核销节点，完善了服务流程

### 5. 新增订单大厅功能

- 新增了订单大厅（hall方法），展示可接单的订单
- 根据师傅的技能和服务区域自动筛选适合的订单
- 提供预计佣金计算，帮助师傅选择订单

## 三、接口规范优化

- 统一了所有接口的返回格式，包含code、message和data三个字段
- 统一了错误处理逻辑，提供了明确的错误提示
- 对敏感操作增加了权限验证和状态检查，避免非法操作
- 增加了数据库事务处理，确保数据一致性

## 四、测试建议

建议针对以下几个关键点进行重点测试：

1. 订单状态流转的完整流程测试
2. 支付功能测试，包括各种支付方式和退款场景
3. 图片上传功能测试
4. 订单核销流程测试
5. 师傅接单和设定服务费功能测试
6. 用户评价功能测试

## 五、注意事项

1. 支付功能当前为模拟实现，实际部署时需要对接真实支付网关
2. 退款功能需要与实际支付渠道对接，确保退款到账
3. 图片上传依赖于外部存储服务，需确保服务稳定可靠
4. 核销码机制依赖于二维码生成库，需确保相关依赖已正确安装 
