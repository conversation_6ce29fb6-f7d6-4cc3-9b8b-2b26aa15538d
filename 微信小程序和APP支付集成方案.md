# 微信小程序和APP支付集成方案

## 1. 方案概述

### 1.1 目标
在现有支付系统基础上，增加对微信小程序支付和手机APP支付的支持，实现多端统一的支付体验。

### 1.2 技术架构
- 基于现有的 PaymentFactory 工厂模式
- 扩展 WechatPaymentService 支持多种客户端类型
- 优化 PaymentController 接口，支持客户端类型识别
- 配置管理支持多个微信应用

### 1.3 支持的支付场景
- **小程序支付**：微信小程序内直接支付（`wechat_miniprogram`）
- **APP支付**：原生APP内调起微信支付（`wechat_app`）
- **H5支付**：手机浏览器内支付（`wechat`，保持现有逻辑）

## 2. 技术实现方案

### 2.1 支付方式扩展

#### 2.1.1 支付方式定义
通过扩展 `payment_method` 参数来支持不同的微信支付方式，保持现有接口兼容性：

**支持的支付方式**：
- `wechat` - 微信H5支付（保持现有逻辑）
- `wechat_miniprogram` - 微信小程序支付
- `wechat_app` - 微信APP支付
- `alipay` - 支付宝支付（现有）
- `balance` - 余额支付（现有）

#### 2.1.2 支付方式验证
```php
protected function validatePaymentMethod(string $paymentMethod): bool
{
    $supportedMethods = ['wechat', 'wechat_miniprogram', 'wechat_app', 'alipay', 'balance'];
    return in_array($paymentMethod, $supportedMethods);
}

protected function getClientTypeFromPaymentMethod(string $paymentMethod): string
{
    switch ($paymentMethod) {
        case 'wechat_miniprogram':
            return 'miniprogram';
        case 'wechat_app':
            return 'app';
        case 'wechat':
        default:
            return 'h5';
    }
}
```

### 2.2 配置文件扩展

#### 2.2.1 环境变量配置
```env
# 微信小程序
WECHAT_MINIPROGRAM_APP_ID=wxxxxxxxxxxxxxxxxxxx
# 微信APP
WECHAT_APP_ID=wxxxxxxxxxxxxxxxxxxx
# H5支付（保持现有配置）
WECHAT_H5_APP_ID=wxxxxxxxxxxxxxxxxxxx
# 公共配置
WECHAT_MCH_ID=1234567890
WECHAT_KEY=your_wechat_key
WECHAT_CERT_PATH=/path/to/cert.pem
WECHAT_KEY_PATH=/path/to/key.pem
```

#### 2.2.2 配置文件结构
```php
// config/wechat.php
return [
    'payment' => [
        'default' => [ // 保持现有H5支付配置
            'app_id' => env('WECHAT_H5_APP_ID'),
            'mch_id' => env('WECHAT_MCH_ID'),
            'key' => env('WECHAT_KEY'),
            'cert_path' => env('WECHAT_CERT_PATH'),
            'key_path' => env('WECHAT_KEY_PATH'),
        ],
        'miniprogram' => [
            'app_id' => env('WECHAT_MINIPROGRAM_APP_ID'),
            'mch_id' => env('WECHAT_MCH_ID'),
            'key' => env('WECHAT_KEY'),
            'cert_path' => env('WECHAT_CERT_PATH'),
            'key_path' => env('WECHAT_KEY_PATH'),
        ],
        'app' => [
            'app_id' => env('WECHAT_APP_ID'),
            'mch_id' => env('WECHAT_MCH_ID'),
            'key' => env('WECHAT_KEY'),
            'cert_path' => env('WECHAT_CERT_PATH'),
            'key_path' => env('WECHAT_KEY_PATH'),
        ],
    ],
];
```

### 2.3 API接口调整

#### 2.3.1 支付创建接口
**接口**: `POST /api/user/payments/create`

**请求参数示例**:

**小程序支付**:
```json
{
    "order_id": 123,
    "payment_type": "appointment",
    "payment_method": "wechat_miniprogram",
    "openid": "oxxxxxxxxxxxxxxxxxxxxxx"
}
```

**APP支付**:
```json
{
    "order_id": 123,
    "payment_type": "appointment",
    "payment_method": "wechat_app"
}
```

**H5支付**:
```json
{
    "order_id": 123,
    "payment_type": "appointment",
    "payment_method": "wechat"
}
```

**参数说明**:
- `payment_method`: 支付方式，新增支持：wechat_miniprogram、wechat_app
- `openid`: 微信用户openid（仅小程序支付时必传）
- 其他参数保持现有接口完全不变

#### 2.3.2 响应数据格式

**小程序支付响应**:
```json
{
    "code": 0,
    "message": "支付订单创建成功",
    "data": {
        "payment_id": 123,
        "payment_no": "PAY20241217123456",
        "client_type": "miniprogram",
        "payment_params": {
            "timeStamp": "1640966400",
            "nonceStr": "randomstring",
            "package": "prepay_id=wx123456789",
            "signType": "RSA",
            "paySign": "signature"
        }
    }
}
```

**APP支付响应**:
```json
{
    "code": 0,
    "message": "支付订单创建成功",
    "data": {
        "payment_id": 123,
        "payment_no": "PAY20241217123456",
        "client_type": "app",
        "payment_params": {
            "appid": "wxxxxxxxxxxx",
            "partnerid": "1234567890",
            "prepayid": "wx123456789",
            "package": "Sign=WXPay",
            "noncestr": "randomstring",
            "timestamp": 1640966400,
            "sign": "signature"
        }
    }
}
```

**H5支付响应**:
```json
{
    "code": 0,
    "message": "支付订单创建成功",
    "data": {
        "payment_id": 123,
        "payment_no": "PAY20241217123456",
        "client_type": "h5",
        "mweb_url": "https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx123456789"
    }
}
```

### 2.4 核心代码实现

#### 2.4.1 PaymentController改进
```php
public function create(Request $request)
{
    $validator = Validator::make($request->all(), [
        'order_id' => 'nullable|exists:orders,id',
        'material_order_id' => 'nullable|exists:material_orders,id',
        'payment_type' => 'required|in:appointment,service,material,recharge,final',
        'payment_method' => 'required|in:wechat,wechat_miniprogram,wechat_app,alipay,balance', // 扩展支付方式
        'openid' => 'required_if:payment_method,wechat_miniprogram|string', // 小程序支付必传
        'coupon_id' => 'nullable|exists:user_coupons,id',
        'amount' => 'required_if:payment_type,recharge|numeric|min:0.01',
    ], [
        'payment_method.in' => '不支持的支付方式',
        'openid.required_if' => '小程序支付需要提供用户openid',
    ]);

    if ($validator->fails()) {
        return ResponseService::validationError($validator->errors());
    }

    $user = Auth::guard('user')->user();
    $paymentMethod = $request->input('payment_method');

    // 根据支付方式获取客户端类型
    $clientType = $this->getClientTypeFromPaymentMethod($paymentMethod);

    // ... 现有逻辑保持不变
    
    $paymentData = [
        'user_id' => $user->id,
        'order_id' => $request->input('order_id'),
        'material_order_id' => $request->input('material_order_id'),
        'amount' => $finalAmount,
        'payment_type' => $paymentType,
        'payment_method' => $paymentMethod,
        'options' => [
            'original_amount' => $amount,
            'discount_amount' => $discountAmount,
            'coupon_id' => $request->input('coupon_id'),
            'client_type' => $clientType, // 从支付方式推导出客户端类型
            'openid' => $request->input('openid'),
        ]
    ];

    // ... 其余逻辑
}

/**
 * 根据支付方式获取客户端类型
 */
protected function getClientTypeFromPaymentMethod(string $paymentMethod): string
{
    switch ($paymentMethod) {
        case 'wechat_miniprogram':
            return 'miniprogram';
        case 'wechat_app':
            return 'app';
        case 'wechat':
        default:
            return 'h5';
    }
}
```

#### 2.4.2 WechatPaymentService扩展
```php
public function createOrder(Payment $payment, array $options = []): array
{
    // 根据支付方式确定客户端类型
    $paymentMethod = $payment->payment_method;
    
    switch ($paymentMethod) {
        case 'wechat_miniprogram':
            return $this->createMiniprogramOrder($payment, $options);
        case 'wechat_app':
            return $this->createAppOrder($payment, $options);
        case 'wechat':
        default:
            return $this->createH5Order($payment, $options);
    }
}

protected function createMiniprogramOrder(Payment $payment, array $options = []): array
{
    try {
        $config = config('wechat.payment.miniprogram');
        $app = Factory::payment($config);
        
        $result = $app->order->unify([
            'trade_type' => 'JSAPI',
            'body' => $this->getOrderBody($payment),
            'out_trade_no' => $payment->payment_no,
            'total_fee' => $this->formatAmount($payment->amount),
            'notify_url' => route('payments.callback', ['method' => 'wechat']),
            'openid' => $options['openid'] ?? '',
        ]);
        
        if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
            $paymentParams = $app->jssdk->bridgeConfig($result['prepay_id'], false);
            
            return [
                'success' => true,
                'data' => [
                    'client_type' => 'miniprogram',
                    'payment_params' => $paymentParams,
                    'prepay_id' => $result['prepay_id'],
                ]
            ];
        }
        
        return [
            'success' => false,
            'message' => $result['return_msg'] ?? '小程序支付订单创建失败'
        ];
    } catch (\Exception $e) {
        $this->logPayment('error', '小程序支付创建异常', [
            'payment_id' => $payment->id,
            'error' => $e->getMessage()
        ]);
        
        return [
            'success' => false,
            'message' => '小程序支付服务异常'
        ];
    }
}

protected function createH5Order(Payment $payment, array $options = []): array
{
    try {
        $config = config('wechat.payment.default'); // 使用现有的default配置
        $app = Factory::payment($config);
        
        $result = $app->order->unify([
            'trade_type' => 'MWEB',
            'body' => $this->getOrderBody($payment),
            'out_trade_no' => $payment->payment_no,
            'total_fee' => $this->formatAmount($payment->amount),
            'notify_url' => route('payments.callback', ['method' => 'wechat']),
            'scene_info' => json_encode([
                'h5_info' => [
                    'type' => 'Wap',
                    'wap_url' => config('app.url'),
                    'wap_name' => config('app.name'),
                ]
            ]),
        ]);
        
        if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
            return [
                'success' => true,
                'data' => [
                    'client_type' => 'h5',
                    'mweb_url' => $result['mweb_url'],
                    'prepay_id' => $result['prepay_id'],
                ]
            ];
        }
        
        return [
            'success' => false,
            'message' => $result['return_msg'] ?? 'H5支付订单创建失败'
        ];
    } catch (\Exception $e) {
        $this->logPayment('error', 'H5支付创建异常', [
            'payment_id' => $payment->id,
            'error' => $e->getMessage()
        ]);
        
        return [
            'success' => false,
            'message' => 'H5支付服务异常'
        ];
    }
}
```

## 3. 前端集成指南

### 3.1 小程序端集成

#### 3.1.1 支付调用
```javascript
// 创建支付订单
const createPayment = async (orderData) => {
  const res = await wx.request({
    url: '/api/user/payments/create',
    method: 'POST',
    header: {
      'Authorization': 'Bearer ' + wx.getStorageSync('token')
    },
    data: {
      ...orderData,
      payment_method: 'wechat_miniprogram', // 指定小程序支付
      openid: wx.getStorageSync('openid') // 小程序支付必传
    }
  });
  
  if (res.data.code === 0) {
    return res.data.data;
  } else {
    throw new Error(res.data.message);
  }
};

// 调起支付
const payOrder = async (orderData) => {
  try {
    const paymentData = await createPayment(orderData);
    
    const result = await wx.requestPayment(paymentData.payment_params);
    
    // 支付成功
    console.log('支付成功', result);
    return true;
  } catch (error) {
    console.error('支付失败', error);
    return false;
  }
};
```

### 3.2 APP端集成

#### 3.2.1 支付调用（React Native示例）
```javascript
import { WeChat } from 'react-native-wechat-lib';

const createPayment = async (orderData) => {
  const response = await fetch('/api/user/payments/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      ...orderData,
      payment_method: 'wechat_app' // 指定APP支付
    })
  });
  
  const result = await response.json();
  if (result.code === 0) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
};

const payOrder = async (orderData) => {
  try {
    const paymentData = await createPayment(orderData);
    
    const result = await WeChat.pay(paymentData.payment_params);
    
    console.log('支付成功', result);
    return true;
  } catch (error) {
    console.error('支付失败', error);
    return false;
  }
};
```

## 4. 部署和配置

### 4.1 微信支付配置

#### 4.1.1 申请微信支付
1. **小程序支付**：在微信小程序后台申请微信支付功能
2. **APP支付**：在微信开放平台申请APP支付功能
3. **H5支付**：在微信商户平台申请H5支付功能

#### 4.1.2 配置回调地址
- 统一回调地址：`https://yourdomain.com/api/user/payments/callback?payment_method=wechat`
- 确保回调地址可以正常访问且支持POST请求

### 4.2 安全配置

#### 4.2.1 域名配置
- 小程序：在小程序后台配置服务器域名
- APP：确保证书文件路径正确
- H5：配置H5支付授权域名

#### 4.2.2 IP白名单
在微信商户平台配置服务器IP白名单，确保回调通知能正常接收。

## 5. 测试方案

### 5.1 开发环境测试

#### 5.1.1 沙箱环境
使用微信支付沙箱环境进行开发测试：
- 申请沙箱账号
- 配置沙箱参数
- 使用测试用例验证功能

#### 5.1.2 测试用例
```php
// 测试用例示例
public function testMiniprogramPayment()
{
    $response = $this->postJson('/api/user/payments/create', [
        'order_id' => 1,
        'payment_type' => 'appointment',
        'payment_method' => 'wechat_miniprogram', // 小程序支付
        'openid' => 'test_openid'
    ]);
    
    $response->assertStatus(200)
             ->assertJsonStructure([
                 'code',
                 'message',
                 'data' => [
                     'payment_id',
                     'payment_no',
                     'client_type',
                     'payment_params'
                 ]
             ]);
}

public function testAppPayment()
{
    $response = $this->postJson('/api/user/payments/create', [
        'order_id' => 1,
        'payment_type' => 'appointment',
        'payment_method' => 'wechat_app' // APP支付，不需要openid
    ]);
    
    $response->assertStatus(200)
             ->assertJsonStructure([
                 'code',
                 'message',
                 'data' => [
                     'payment_id',
                     'payment_no',
                     'client_type',
                     'payment_params'
                 ]
             ]);
}

public function testH5Payment()
{
    $response = $this->postJson('/api/user/payments/create', [
        'order_id' => 1,
        'payment_type' => 'appointment',
        'payment_method' => 'wechat' // H5支付，保持现有逻辑
    ]);
    
    $response->assertStatus(200)
             ->assertJsonStructure([
                 'code',
                 'message',
                 'data' => [
                     'payment_id',
                     'payment_no',
                     'client_type',
                     'mweb_url'
                 ]
             ]);
}
```

### 5.2 生产环境测试

#### 5.2.1 小额测试
使用小额订单进行真实支付测试，验证：
- 支付流程完整性
- 回调处理正确性
- 订单状态更新准确性

#### 5.2.2 异常场景测试
- 网络异常处理
- 支付超时处理
- 重复支付处理

## 6. 监控和维护

### 6.1 日志监控

#### 6.1.1 关键日志
- 支付订单创建日志
- 支付回调处理日志
- 支付异常错误日志

#### 6.1.2 日志示例
```php
Log::info('微信小程序支付创建', [
    'payment_id' => $payment->id,
    'client_type' => 'miniprogram',
    'amount' => $payment->amount,
    'openid' => $options['openid']
]);
```

### 6.2 性能监控

#### 6.2.1 关键指标
- 支付成功率
- 支付响应时间
- 回调处理时间

#### 6.2.2 告警配置
- 支付失败率超过阈值告警
- 回调处理失败告警
- 支付金额异常告警

## 7. 常见问题解决

### 7.1 小程序支付问题

#### 7.1.1 openid获取失败
**问题**：无法获取用户openid
**解决**：
- 检查小程序登录流程
- 确认授权scope设置
- 验证小程序配置

#### 7.1.2 支付签名错误
**问题**：支付时提示签名错误
**解决**：
- 检查小程序appid配置
- 验证商户号和密钥
- 确认支付参数格式

### 7.2 APP支付问题

#### 7.2.1 无法调起支付
**问题**：APP内无法调起微信支付
**解决**：
- 检查微信SDK集成
- 确认APP在微信开放平台注册
- 验证包名和签名

#### 7.2.2 支付结果获取失败
**问题**：支付完成后无法获取结果
**解决**：
- 检查支付回调处理
- 确认结果页面逻辑
- 验证订单状态查询

### 7.3 通用问题

#### 7.3.1 回调通知失败
**问题**：微信支付回调通知失败
**解决**：
- 检查服务器网络连通性
- 确认回调地址配置
- 验证IP白名单设置

#### 7.3.2 证书配置错误
**问题**：退款等操作需要证书认证失败
**解决**：
- 检查证书文件路径
- 确认证书文件格式
- 验证证书权限设置

## 8. 后续优化建议

### 8.1 功能优化
- 支持微信支付分账功能
- 增加支付风控机制
- 优化支付性能和稳定性

### 8.2 安全优化
- 实现支付接口限流
- 增加支付异常检测
- 完善支付日志审计

### 8.3 用户体验优化
- 支付状态实时更新
- 支付失败自动重试
- 支付过程用户引导

---

**文档版本**：v1.0  
**最后更新**：2024-12-17  
**维护人员**：开发团队 
