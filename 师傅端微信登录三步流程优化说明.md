# 师傅端微信登录三步流程优化说明

## 问题描述

用户提出了一个重要的逻辑问题：

> "bindPhone 这个接口需要登录的话，那我授权登录也没有返回token啊"

这个问题指出了微信登录流程中的一个设计缺陷。

## 问题分析

### 原始流程存在的问题

1. **第一步 `wechatAuthorizedLogin`**：
   - 如果师傅已存在且已绑定微信 → 返回 token（正常）
   - 如果师傅不存在 → 返回 `need_phone` 状态，但**没有返回 token**

2. **第二步 `wechatBindPhone`**：
   - 用户误以为需要 token 验证
   - 实际上在中间件中设置为不需要登录，但这样的设计让人困惑

3. **逻辑矛盾**：
   - 如果第一步没有返回 token，前端无法进行身份验证
   - 如果第二步需要 token，但第一步没有提供，形成死循环

## 解决方案

### 1. 明确设计原则

**微信登录流程设计原则**：
- 所有微信相关的登录/注册接口都不需要 token 验证
- 这是因为用户在微信登录过程中还没有获得 token，需要完成整个流程后才能获得
- 安全性通过微信的授权码(code)和 OpenID 来保证

### 2. 中间件配置

在 `AuthController` 构造函数中，以下接口不需要 token 验证：

```php
$this->middleware('worker.auth', ['except' => [
    'login',
    'register',
    'sendVerifyCode',
    'wxLogin',
    'wechatSilentLogin',
    'wechatAuthorizedLogin',    // 第一步：不需要token
    'wechatBindPhone',          // 第二步：不需要token
    'wechatCompleteProfile',    // 第三步：不需要token
    'wechatBindRegister',       // 保持向后兼容
    'resetPassword'
]]);
```

### 3. 流程说明

#### 第一步：微信授权登录
- **接口**：`POST /api/worker/wechat-authorized-login`
- **无需 token 验证**
- **功能**：获取微信基本授权信息，检查是否已有师傅账号
- **返回**：
  - 如果师傅已存在 → 直接返回 token
  - 如果师傅不存在 → 返回 `need_phone` 状态和 openid 等信息

#### 第二步：绑定手机号
- **接口**：`POST /api/worker/wechat-bind-phone`
- **无需 token 验证**
- **功能**：获取并绑定手机号，支持账号合并
- **返回**：
  - 如果手机号已注册且未绑定微信 → 合并账号，返回 token
  - 如果手机号未注册 → 返回 `need_profile` 状态

#### 第三步：完善师傅信息
- **接口**：`POST /api/worker/wechat-complete-profile`
- **无需 token 验证**
- **功能**：完善师傅注册所需的详细信息
- **返回**：注册成功，返回 token

## 安全机制

### 为什么不需要 token 验证？

1. **流程特性**：用户在微信登录过程中还没有获得 token
2. **安全保证**：通过微信的授权机制和 OpenID 来确保安全性
3. **用户体验**：避免循环依赖，简化登录流程

### 安全保障措施

1. **授权码验证**：每个微信授权码只能使用一次
2. **OpenID 唯一性**：微信 OpenID 在应用内唯一
3. **时效性**：授权码和 session_key 都有时效限制
4. **手机号验证**：通过微信官方接口获取手机号，确保真实性
5. **短信验证码**：在完善师傅信息时需要验证短信验证码

## 前端调用示例

```javascript
// 第一步：基础授权
wx.login({
    success: async (res) => {
        if (res.code) {
            const response = await request({
                url: '/api/worker/wechat-authorized-login',
                method: 'POST',
                data: {
                    code: res.code,
                    platform: 'miniprogram'
                }
                // 注意：这里不需要携带 token
            });

            if (response.code === 200) {
                // 登录成功，保存 token
                wx.setStorageSync('worker_token', response.data.access_token);
            } else if (response.code === 203) {
                // 需要绑定手机号，进入第二步
                this.bindPhone(response.data);
            }
        }
    }
});

// 第二步：绑定手机号
bindPhone(wechatData) {
    wx.getPhoneNumber({
        success: async (res) => {
            const response = await request({
                url: '/api/worker/wechat-bind-phone',
                method: 'POST',
                data: {
                    phone_code: res.code,
                    openid: wechatData.openid,
                    unionid: wechatData.unionid,
                    session_key: wechatData.session_key,
                    platform: 'miniprogram'
                }
                // 注意：这里也不需要携带 token
            });

            if (response.code === 200) {
                // 账号合并成功，保存 token
                wx.setStorageSync('worker_token', response.data.access_token);
            } else if (response.code === 204) {
                // 需要完善师傅信息，进入第三步
                this.completeProfile(response.data);
            }
        }
    });
}

// 第三步：完善师傅信息
async completeProfile(data) {
    const response = await request({
        url: '/api/worker/wechat-complete-profile',
        method: 'POST',
        data: {
            openid: data.openid,
            phone: data.phone,
            platform: 'miniprogram',
            // ... 其他师傅信息
        }
        // 注意：这里同样不需要携带 token
    });

    if (response.code === 200) {
        // 注册成功，保存 token
        wx.setStorageSync('worker_token', response.data.access_token);
    }
}
```

## 状态码说明

| 状态码 | 说明 | 下一步操作 |
|--------|------|-----------|
| 200 | 成功（登录/注册/合并） | 保存 token，跳转到主页 |
| 202 | 需要授权登录 | 引导用户进行授权登录 |
| 203 | 需要绑定手机号 | 调用第二步接口 |
| 204 | 需要完善师傅信息 | 调用第三步接口 |
| 409 | 冲突（手机号或身份证已存在） | 提示用户 |
| 422 | 验证失败 | 检查参数，重新提交 |

## 优化效果

1. **逻辑清晰**：三步流程都不需要 token，避免循环依赖
2. **安全可靠**：通过微信授权机制保证安全性
3. **用户体验**：流程顺畅，无需中断
4. **向后兼容**：保留旧的 `wechatBindRegister` 接口

## 总结

通过这次优化，我们解决了微信登录流程中的逻辑矛盾问题，明确了设计原则，并提供了清晰的文档说明。现在整个微信登录流程更加合理和用户友好。 
