# 项目进度记录

## 2025-01-27
- **新增功能**: 阿里云短信服务集成
- **涉及文件**:
  - `app/Services/SmsService.php` - 短信服务类（新建）
  - `config/sms.php` - 短信配置文件（新建）
  - `app/Http/Controllers/Api/User/AuthController.php` - 用户认证控制器（修改）
  - `env.sms.example` - 短信环境配置示例（新建）
  - `docs/sms-service-integration.md` - 短信服务集成文档（新建）
  - `README.md` - 项目说明文档（更新）
  - `composer.json` - 添加阿里云SDK依赖
- **功能描述**:
  - 集成阿里云短信服务SDK，支持真实短信发送
  - 创建短信服务类，封装发送逻辑、频率限制、格式验证等功能
  - 支持开发环境日志模拟和生产环境真实发送两种模式
  - 实现手机号格式验证、发送频率限制（1分钟间隔）
  - 支持多种验证码类型：注册、登录、重置密码、更新手机号
  - 可配置验证码长度（默认6位）、过期时间（默认5分钟）
  - 完整的错误处理机制和详细日志记录
  - 修改AuthController的sendVerifyCode方法，使用SmsService发送短信
  - 生产环境不返回验证码，开发环境调试时可返回
- **配置要求**:
  - 阿里云短信服务开通和配置（AccessKey、签名、模板）
  - 环境变量配置：SMS_DRIVER、ALIYUN_ACCESS_KEY_ID等
  - 开发环境可设置SMS_DRIVER=log使用日志模拟
- **技术特性**:
  - 依赖注入和服务容器化设计
  - 支持多驱动切换（阿里云/日志模拟）
  - 缓存机制实现发送频率控制
  - 严格的参数验证和安全机制
  - 兼容现有验证码业务逻辑

## 2025-01-27
- **修复问题**: 微信支付回调签名验证失败
- **涉及文件**: 
  - `app/Services/Payment/WechatPaymentService.php` - 微信支付服务类
- **问题描述**: 
  - 微信支付回调时签名验证失败，导致支付成功但回调处理失败
  - 原因是签名字符串包含了非微信官方字段 `method`，该字段是系统自定义用于区分支付方式的字段
  - 微信官方签名验证不应包含自定义字段
- **解决方案**:
  - 在 `buildSignStringV2` 方法中将 `method` 字段添加到排除列表
  - 修改排除字段从 `['sign', 'payment_method']` 为 `['sign', 'payment_method', 'method']`
  - 确保只有微信官方回调字段参与签名验证
- **技术细节**:
  - 排除字段包括：`sign`（签名字段）、`payment_method`（系统字段）、`method`（系统字段）
  - 签名验证遵循微信支付v2接口规范
  - 修复后微信支付回调能够正常验证签名并处理支付成功逻辑

## 2025-01-27
- **新增功能**: 公开订单大厅接口（无需登录）
- **涉及文件**: 
  - `app/Http/Controllers/Api/Worker/OrderController.php` - 师傅订单控制器
  - `routes/api.php` - API路由文件
  - `docs/api/worker/order.md` - 订单接口文档
- **功能描述**: 
  - 新增`publicHall`方法，提供不需要登录的订单大厅功能
  - 移除了师傅登录验证、年费套餐检查、技能筛选、区域筛选等限制条件
  - 保留基本的分页和排序功能（按预约费降序或按时间降序）
  - 返回订单基本信息，包括服务类型、预约时间、地址、费用等
  - 对联系电话进行脱敏处理（中间4位显示为****）
  - 不显示预计佣金信息（因为无师傅登录信息）
  - 只显示允许师傅自行接单的服务订单
- **接口路径**: `GET /api/orders/public-hall`
- **技术细节**:
  - 查询条件：`status = 待接单` + `worker_id = null` + `service.can_self_accept = 1`
  - 支持参数：order_type（排序）、page（页码）、limit（每页数量）
  - 返回完整分页信息：current_page、per_page、total_pages、has_more

## 2023-07-10
- 创建项目基础结构
- 配置数据库连接
- 安装Dcat Admin管理后台

## 2023-07-15
- 完成用户管理模块
- 完成师傅管理模块
- 完成服务分类管理

## 2023-07-20
- 完成服务管理模块
- 完成技能管理模块
- 完成订单管理基础功能

## 2023-07-25
- 完成支付管理模块
- 完成材料管理模块
- 完成区域管理
- 添加订单评价批量审核功能
  - 创建`BatchApproveOrderReview`和`BatchRejectOrderReview`批量操作类
  - 在`OrderReviewController`中集成批量审核功能
  - 实现批量通过和批量拒绝订单评价的功能
  - 添加事务处理确保批量操作的数据一致性
  - 统一批量操作的响应格式和提示信息
  - 参考`WorkerSkillController`中的批量审核实现
  - 涉及文件：
    - `app/Admin/Actions/BatchApproveOrderReview.php`（新增）
    - `app/Admin/Actions/BatchRejectOrderReview.php`（新增）
    - `app/Admin/Controllers/OrderReviewController.php`（修改）

## 2023-07-30
- 完成内容管理模块
- 完成营销管理模块
- 完成系统参数设置

## 2023-08-05
- 完成API接口开发
- 完成小程序端基础功能
- 完善用户充值功能
- 优化订单支付流程
- 添加支付回调处理机制

## 2023-08-10
- 完成师傅端APP开发
- 完成用户端APP开发
- 新增用户消费记录模块
  - 创建后台管理控制器 UserConsumptionController
  - 创建用户端API控制器 ConsumptionController
  - 完善钱包交易记录模型，添加关联关系
  - 新增用户消费记录查询接口
  - 新增用户消费统计接口
  - 添加相关路由配置
  - 在用户管理页面添加消费记录快捷入口

## 2023-08-15
- 完成系统测试
- 修复BUG
- 优化系统性能
- 添加了订单完成时自动计算师傅佣金的功能：
  - 在`OrderController.php`的`complete`方法中添加计算和生成师傅佣金的逻辑
  - 新增`generateWorkerCommission`方法，实现：
    - 根据师傅等级获取对应佣金比例
    - 计算佣金金额（服务费 × 佣金比例）
    - 创建师傅收益记录
    - 更新师傅余额和累计收益
    - 记录详细的操作日志
  - 佣金计算规则：
    - 优先使用师傅等级中设置的佣金比例
    - 如无等级或等级佣金比例为0，则使用系统默认佣金比例
    - 仅当佣金金额大于0时才创建收益记录

## 2023-09-01
- 项目上线

## 2023-09-15
- 添加材料订单管理功能
- 添加评价审核管理功能
- 优化管理后台功能结构

## 2023-11-03
- 创建项目基础结构
- 实现用户认证功能
- 搭建后台管理框架

## 2023-11-10
- 实现订单管理基础功能
- 添加服务分类功能
- 开发师傅接单流程

## 2023-11-17
- 完成支付功能集成
- 优化订单状态流转
- 添加用户钱包功能

## 2023-11-24
- 实现材料管理功能
- 添加订单评价功能
- 优化后台数据统计

## 2023-12-01
- 实现优惠券领取和使用功能
- 添加营销活动模块
- 优化用户注册流程

## 2023-12-08
- 改进派单功能
  - 在派单时增加了基于服务地址(区域)筛选师傅的功能
  - 在派单表单中只显示服务区域覆盖订单区域的师傅
  - 在派单确认时增加验证，确保师傅的服务区域包含订单区域
  - 修复派单后订单状态更新的问题，确保状态正确变更为"待师傅上门"
  - 修复状态日志记录，确保记录准确的状态变更
  - 涉及文件: `app/Admin/Controllers/OrderController.php`

## 2024-05-12
- 为优惠券添加条件标题功能
  - 在CouponController中添加formatConditionTitle方法，用于生成优惠券使用条件的说明文本
  - 在优惠券列表、使用优惠券、领取优惠券接口中添加condition_title字段
  - 条件标题包括：适用范围（全场/材料/服务）、最低订单金额、有效期等

## 2024-05-20
- 添加违约费用设置查询接口
  - 创建SettingController，新增cancellationFees方法，用于获取违约费用设置信息
  - 在API路由中添加/cancellation-fees接口
  - 按照服务前小时数降序排列违约费用规则，方便用户查看

## 2024-06-04
- 开发首页数据聚合接口
  - 在IndexController中实现index方法，提供招聘、课程和服务分类的数据
  - 招聘部分：获取推荐的职位信息，包含职位名称、公司名称和薪资范围
  - 课程分类：采用树状结构展示课程分类，包含一级和二级分类
  - 服务分类：获取所有服务类别，包含分类图片和子分类信息
  - 统一使用ResponseService进行数据返回，保持API风格一致

## 2024-06-25
- 添加用户评论列表获取功能
  - 在OrderController中新增reviewList方法，用于获取用户的所有评论列表
  - 支持按照服务ID、师傅ID、评分和状态进行筛选
  - 添加分页功能，默认每页10条，最多50条记录
  - 创建详细API文档：docs/api/user/review-list.md
  - 更新主API文档，添加评论列表接口说明
  - 新增路由：/api/orders/reviews

## 2024-07-05
- 优化师傅账号审核流程和权限控制
  - 新增WorkerApprovalCheck中间件，用于检查师傅账号审核状态
  - 修改WorkerAuth中间件，分离登录验证和审核状态检查功能
  - 调整AuthController的中间件配置，实现未审核师傅只能访问有限功能
  - 未审核师傅仅允许登录、查看个人信息、登出和刷新令牌
  - 修改login和wxLogin方法，允许未审核师傅登录但提供审核状态提示
  - 在me方法和respondWithToken方法中添加审核状态提示信息
  - 整体改进师傅端用户体验，提供清晰的审核状态反馈

## 2024-07-08
- 实现师傅年费套餐验证功能
  - 在OrderController中添加checkValidAnnualPackage方法，用于检查师傅是否有有效年费套餐
  - 修改接单方法accept，添加年费套餐验证，未购买套餐的师傅无法接单
  - 修改服务大厅方法hall，添加年费套餐验证，未购买套餐的师傅无法查看订单大厅
  - 统一错误提示，引导师傅购买年费套餐
  - 优化代码结构，复用验证逻辑

## 2024-07-09
### 修复视图文件错误
- 修复 `resources/views/admin/order/review.blade.php` 中的闭包问题，添加默认值确保 `$review` 变量非闭包，并添加对 `images` 属性的类型检查
- 修复 `resources/views/admin/order/material-orders.blade.php` 中的潜在闭包问题，确保 `$materialOrders` 变量为集合而非闭包
- 解决了 "Call to undefined method Closure::count()" 错误

## 2024-12-19
- **修改功能**: 订单管理中材料费用计算逻辑优化
- **涉及文件**: 
  - `app/Admin/Controllers/OrderController.php` - 订单管理控制器
- **修改内容**: 
  - 修改订单列表中材料费用显示逻辑，不再只计算已支付状态的材料订单
  - 改为计算除待付款和已取消状态外的所有材料订单费用
  - 更新订单详情页面的材料费用显示，使用相同的计算逻辑
  - 修改表单保存时的材料费用计算和总金额计算逻辑
  - 确保材料费用统计更加准确，包含已付款、待上传使用图片、待客户确认、已完成等状态的材料订单
- **技术细节**:
  - 将 `where('status', MaterialOrder::STATUS_PAID)` 改为 `whereNotIn('status', [MaterialOrder::STATUS_PENDING_PAYMENT, MaterialOrder::STATUS_CANCELLED])`
  - 涉及订单列表、详情页和表单保存三个场景的材料费用计算
  - 保持了代码的一致性和数据的准确性

## 2024-07-15
- 添加材料订单和评价审核后台管理功能
  - 创建MaterialOrderController和OrderReviewController控制器
  - 为两个控制器添加后台路由配置
  - 创建SQL脚本添加后台菜单项和权限配置
  - 将权限分配给管理员角色
  - 添加菜单图标：材料订单使用shopping-bag图标，评价审核使用star图标
  - 为材料订单和评价审核功能添加完整的增删改查操作

## 2023-06-01
- 完善订单详情页功能
  - 重新设计订单详情页布局，使用分栏和卡片布局提高信息呈现效果

## 2024-12-17
- **新增微信小程序登录兼容方案**
- **涉及文件**:
  - `app/Http/Controllers/Api/User/AuthController.php` - 用户认证控制器
  - `app/Services/WechatLoginService.php` - 微信登录服务类
  - `app/Models/WechatLoginLog.php` - 微信登录日志模型
  - `app/Models/UserMergeLog.php` - 用户合并日志模型
  - `config/wechat.php` - 微信配置文件
  - `routes/api.php` - API路由配置
- **功能说明**:
  - 实现微信静默登录和授权登录功能
  - 支持用户账号自动合并，以手机号为唯一标识
  - 添加完整的登录日志记录和用户合并记录
  - 创建详细的API文档和前端集成示例
  - 支持多平台微信登录（小程序、APP、H5）
- **技术特点**:
  - 使用JWT Token进行用户认证
  - 实现用户数据智能合并机制
  - 添加防重放攻击保护
  - 完善的错误处理和日志记录

## 2025-01-21
- **优化微信绑定手机号接口**
- **涉及文件**:
  - `app/Http/Controllers/Api/User/AuthController.php` - 用户认证控制器
  - `routes/api.php` - API路由配置
  - `docs/api/user/wechat_login_api.md` - API文档
  - `docs/api/user/wechat_login_frontend_example.md` - 前端示例文档
- **修改内容**:
  - 将微信绑定手机号接口移至需要用户认证的路由组中
  - 简化接口参数，只需传递手机号授权码，openid从当前登录用户获取
  - 更新API文档，说明接口需要Bearer Token认证
  - 更新前端示例代码，展示正确的调用方式
- **优化理由**:
  - 用户进入小程序时必然经过静默登录或授权登录流程
  - 绑定手机号时用户已经登录，可以直接从认证用户获取openid
  - 简化前端调用逻辑，提高接口安全性
- **接口变更**:
  - 路由从公开接口移至 `user.auth` 中间件保护
  - 请求参数从 `{phoneCode, openid}` 简化为 `{phoneCode}`
  - 响应保持不变，继续返回更新后的用户信息和token

## 2025-01-21
- **更新微信绑定手机号功能，使用EasyWeChat官方手机号接口**
- **涉及文件**:
  - `app/Services/WechatLoginService.php` - 添加getPhoneNumber方法，使用EasyWeChat的phone_number服务
  - `app/Http/Controllers/Api/User/AuthController.php` - 修改wechatBindPhone方法，使用phoneCode替代短信验证码
  - `docs/api/user/wechat_login_api.md` - 更新API文档，修改绑定手机号接口说明
- **功能说明**:
  - 使用微信官方的wx.getPhoneNumber接口获取用户手机号
  - 不再需要短信验证码，提升用户体验
  - 支持小程序平台的手机号授权
  - 接口参数从phone+code改为phoneCode
  - 添加平台类型验证，只有小程序支持手机号授权
- **技术细节**:
  - 集成EasyWeChat Factory创建小程序实例
  - 使用$app->phone_number->getUserPhoneNumber()获取手机号
  - 添加详细的错误处理和日志记录
  - 更新前端示例代码，使用button的open-type="getPhoneNumber" 
  - `微信小程序登录兼容方案.md` - 完整方案文档
  - `database/migrations/2024_12_17_000000_add_miniprogram_fields_to_users_table.php` - 用户表扩展
  - `database/migrations/2024_12_17_000001_create_user_merge_logs_table.php` - 合并日志表
  - `app/Models/UserMergeLog.php` - 用户合并日志模型
  - `app/Models/User.php` - 用户模型扩展
- **功能特性**:
  - 设计完整的微信小程序登录流程，支持code换取session_key和openid
  - 实现基于unionid的跨平台用户识别机制
  - 创建智能的用户账号合并功能，支持手机号绑定时自动合并
  - 扩展用户表结构，添加unionid、session_key、wx_nickname、wx_avatar、platform_type、merge_source、merged_at字段
  - 新建用户合并日志表，记录所有用户合并操作的完整信息
  - 在User模型中添加平台类型和合并来源常量定义
  - 添加辅助方法：needBindPhone()、isWechatUser()、isMiniprogramUser()等
  - 设计完善的用户数据迁移机制，包括订单、地址、钱包、优惠券等关联数据
  - 保持向后兼容性，现有API接口和业务逻辑不受影响
- **技术实现**:
  - unionid优先的用户识别策略
  - 事务保护的用户数据合并操作
  - 完整的操作日志和审计追踪
  - 多端登录状态统一管理
  - 用户隐私和数据安全保护

## 2024-12-20
- **修复材料费用计算显示问题**
- **涉及文件**: `app/Admin/Controllers/OrderController.php`

## 2024-12-21
- **充值订单余额记录功能开发**
- **涉及文件**: 
  - `app/Models/Recharge.php` - 充值模型
  - `database/migrations/2025_06_21_212116_add_balance_fields_to_recharges_table.php` - 数据库迁移文件
  - `app/Services/Payment/AbstractPaymentService.php` - 抽象支付服务
  - `app/Http/Controllers/Api/User/RechargeController.php` - 充值控制器
  - `app/Admin/Controllers/UserController.php` - 用户管理控制器
  - `sql/04_payments.sql` - 支付相关SQL文件
- **功能说明**:
  - 为充值记录表添加 `balance_before` 和 `balance_after` 字段，用于记录充值前后的余额
  - 修改充值模型，添加新字段到可批量赋值属性和类型转换中
  - 创建数据库迁移文件，为现有充值表添加余额字段
  - 更新所有充值处理逻辑，在充值成功时记录充值前后余额：
    - 完整充值处理（包含赠送金额）
    - 简单充值处理（无赠送金额）
    - 余额充值处理
    - 管理后台充值处理
  - 修改充值状态更新方法，确保余额记录的准确性
  - 更新SQL文件，添加新字段定义
- **技术实现**:
  - 在充值前获取用户当前余额作为 `balance_before`
  - 计算充值后余额作为 `balance_after`
  - 在数据库事务中同时更新钱包余额和充值记录
  - 保持钱包流水记录与充值记录的数据一致性
  - 所有充值相关操作都添加了详细的日志记录

## 2024-12-21
- **充值后余额字段优化需求分析**
- **涉及功能**: 用户充值模块
- **需求背景**: 当前充值功能在某些接口中已经返回了余额信息，但在其他充值相关接口中缺少统一的余额字段返回，用户无法及时获知充值后的最新余额信息
- **生成文档**: 
  - 创建 `充值余额字段优化方案.md` - 完整的充值后余额字段添加方案文档
  - 包含现状分析、优化方案、实施建议、技术实现细节等内容
  - 提供两套方案：快速优化方案和长期服务类方案
  - 制定分阶段实施计划（1-2天快速优化，3-5天完善优化，1-2周长期优化）
- **主要优化点**:
  - 在所有充值相关API接口中统一添加余额字段
  - 新增通用的余额获取方法
  - 可选的数据库字段优化
  - 创建专门的BalanceService服务类
  - 完善错误处理和缓存机制
- **预期效果**: 提升用户体验，让用户能够实时了解自己的钱包余额变化

## 2024-12-17
- **实现微信小程序和APP支付功能**
- **涉及文件**: 
  - `app/Models/Payment.php` - 支付模型，新增小程序和APP支付方式常量
  - `app/Http/Controllers/Api/User/PaymentController.php` - 支付控制器，扩展支付方式验证和客户端类型识别
  - `app/Services/Payment/PaymentFactory.php` - 支付工厂，注册新的支付方式
  - `app/Services/Payment/WechatPaymentService.php` - 微信支付服务，实现多端支付支持
  - `config/wechat.php` - 微信配置文件，新增小程序和APP支付配置
  - `微信小程序和APP支付集成方案.md` - 完整的技术方案文档
- **功能特性**:
  - 新增 `wechat_miniprogram` 和 `wechat_app` 两种支付方式
  - 保持现有 `wechat` H5支付逻辑完全不变，确保向后兼容
  - 根据 `payment_method` 参数自动识别客户端类型并调用对应支付接口
  - 小程序支付需要传递 `openid` 参数，APP支付不需要
  - 支持三种微信支付场景：小程序JSAPI支付、APP支付、H5 MWEB支付
- **技术实现**:
  - 在PaymentController中新增 `getClientTypeFromPaymentMethod` 方法
  - 在WechatPaymentService中实现 `createMiniprogramOrder`、`createAppOrder`、`createH5Order` 三个方法
  - 扩展微信支付配置，支持不同应用的AppID配置
  - 实现APP支付签名生成算法
  - 保持现有接口参数结构不变，仅扩展支付方式选项
- **配置要求**:
  - 需要配置 `WECHAT_MINIPROGRAM_APP_ID` 环境变量（小程序AppID）
  - 需要配置 `WECHAT_APP_ID` 环境变量（APP应用AppID）
  - 商户号、密钥、证书等配置与现有H5支付共用

## 2024-12-21
- **修改功能**: 延长JWT登录token有效期为7天
- **涉及文件**: 
  - `config/jwt.php` - JWT配置文件
  - `app/Http/Controllers/Api/User/AuthController.php` - 用户端认证控制器
  - `app/Http/Controllers/Api/Worker/AuthController.php` - 师傅端认证控制器
- **修改内容**: 
  - 将JWT TTL从默认的60分钟（1小时）延长到10080分钟（7天）
  - 修改config/jwt.php配置文件，将默认TTL值从60改为10080
  - 统一两个AuthController中的expires_in计算逻辑，都使用相同的默认值
  - 添加注释说明TTL以分钟为单位，在API返回时转换为秒
- **技术细节**:
  - JWT配置中TTL以分钟为单位：7天 = 7 × 24 × 60 = 10080分钟
  - API返回中expires_in以秒为单位：10080分钟 × 60 = 604800秒
  - 确保用户端和师傅端登录token有效期一致
  - 提升用户体验，减少频繁重新登录的需求

## 2025-01-17
- **新增离店师傅押金退回功能**
- **涉及文件**: 
  - `app/Admin/Controllers/OfflineWorkerController.php` - 离店师傅管理控制器
  - `app/Admin/routes.php` - 管理后台路由配置
- **修改内容**: 
  - 修复了师傅编号显示的linter错误，使用`$this->getAttribute('id')`替代`$this->id`
  - 在操作列添加"退回押金"按钮，只对还未退回押金的师傅显示
  - 实现押金退回表单页面，包含实际退回金额、退回日期、退回备注等字段
  - 添加押金退回处理逻辑，支持WorkerQuitApplication和WorkerQuitRequest两种类型
  - 修复Form::make的Closure类型错误，改用new Form()方式创建表单
  - 添加完整的数据验证和事务处理，确保押金退回操作的安全性
- **功能特性**:
  - 智能判断师傅退出类型，显示对应的表单字段
  - 自动填充默认退回金额（等于押金金额）
  - 支持部分退回或全额退回
  - 完整的操作记录和状态更新
  - 友好的错误提示和成功反馈
- **路由新增**:
  - `GET /offline-workers/{id}/refund-deposit` - 显示押金退回表单
  - `POST /offline-workers/{id}/process-refund` - 处理押金退回请求

## 2025-01-23
- **修复服务管理中hasMany字段编辑不回显问题**
- **涉及文件**: 
  - `app/Admin/Controllers/ServiceController.php` - 服务管理控制器
  - `app/Models/Service.php` - 服务模型
  - `app/Models/ServiceImage.php` - 服务图片模型
- **修复内容**: 
  - 修复了 ServiceImage 模型中重复 trait 导入和语法错误
  - 在 ServiceController 的 hasMany 表单配置中添加 useTable() 方法
  - 为 Service 模型的 images 关联添加默认排序（按 sort 字段升序）
  - 确保服务图片在编辑表单中能够正确回显
- **技术细节**:
  - 清理了 ServiceImage 模型中重复的 `use HasDateTimeFormatter;` 声明
  - 在 hasMany 配置中使用 `->useTable()` 方法改善编辑体验
  - 添加 `->orderBy('sort', 'asc')` 到 images 关联关系中
  - 保持了图片字段的必填验证和自动上传配置

## 2024-12-26
- **修复设定服务费用逻辑问题**
- **涉及文件**: `app/Http/Controllers/Api/Worker/OrderController.php`
- **修改内容**: 
  - 修复当预约费用和整体服务费用相同时，订单卡在"待设定维修费用"状态的问题
  - 在`setServiceFee`方法中添加逻辑判断，当计算出的服务费用为0或负数时，直接跳过支付环节
  - 自动将订单状态更新为`STATUS_PENDING_SERVICE`（待维修）
  - 设置`service_paid_at`时间戳，表示无需额外支付
  - 优化状态验证逻辑，允许在用户未支付服务费的情况下修改费用
  - 改进返回数据，添加`need_payment`和`auto_advanced`字段，方便前端处理
  - 完善状态日志记录，详细记录费用设定和状态变更情况
- **技术细节**:
  - 增加`calculatedServiceFee`变量计算实际需要支付的服务费用
  - 当`calculatedServiceFee <= 0`时自动进入维修状态
  - 优化错误消息提示，提供更清晰的状态说明
  - 确保状态流转的完整性和准确性

## 2024-12-21
- **修复功能**: 师傅退出申请审核JavaScript错误
- **涉及文件**: 
  - `app/Admin/Controllers/WorkerQuitRequestController.php` - 师傅退出申请管理控制器
- **修改内容**: 
  - 修复点击"通过"按钮时出现JavaScript代码问题
  - 将Dcat Admin的复杂对话框替换为简单的原生JavaScript确认对话框
  - 将Dcat.confirm()替换为confirm()，避免JavaScript执行错误
  - 将Dcat.success/error()替换为alert()，确保提示正常显示
  - 将Dcat.reload()替换为location.reload()，确保页面正常刷新
  - 使用$(document).ready()和$(document).on()确保事件绑定正常工作
- **技术细节**:
  - 简化JavaScript代码，提高兼容性和稳定性
  - 保持原有的AJAX请求逻辑不变
  - 确保通过、拒绝和退款三个操作的JavaScript都正常工作
  - 修复了用户界面交互问题，提升用户体验

## 2025-01-23
### 重构微信支付回调处理机制
- **移除EasyWeChat回调依赖**：完全移除了对EasyWeChat的`handlePaidNotify`方法的依赖，解决了回调处理中的潜在问题
- **实现原生XML解析**：
  - 新增`parseXmlData`方法，使用原生PHP的`simplexml_load_string`解析微信回调XML数据
  - 添加XXE攻击防护，使用`libxml_disable_entity_loader`确保安全性
  - 支持CDATA节点的正确解析
- **实现原生签名验证**：
  - 新增`verifyWechatSignature`方法，实现MD5签名验证算法
  - 新增`buildSignString`方法，按照微信支付规范构建签名字符串
  - 支持参数排序、空值过滤等标准签名流程
- **优化回调处理流程**：
  - 重构`handleCallback`方法，直接从`php://input`读取原始XML数据
  - 改进`processWechatCallback`方法，返回结构化的处理结果
  - 增强错误处理和日志记录，提供详细的调试信息
- **标准化XML响应**：
  - 新增`buildSuccessResponse`和`buildFailResponse`方法
  - 确保回调响应符合微信支付规范
  - 支持自定义错误消息的XML响应
- **更新控制器集成**：
  - 修改`PaymentController`的`callback`方法，正确使用服务返回的XML响应
  - 保持向后兼容性，支持其他支付方式的JSON响应
- **创建测试文件**：
  - 新增`test_wechat_callback_new.php`测试文件
  - 提供完整的单元测试覆盖，验证XML解析、签名验证等核心功能

### 微信支付v2接口专项优化
- **XML解析优化**：
  - 新增`xmlToArray`方法，正确处理SimpleXMLElement转换
  - 解决Array to string conversion警告问题
  - 确保所有字段都被正确转换为字符串类型
- **v2接口签名规范**：
  - 新增`buildSignStringV2`方法，严格按照v2接口规范处理签名
  - 正确过滤空值参数（v2接口特性：空值不参与签名）
  - 确保参数按ASCII码排序，使用MD5算法
- **字段验证增强**：
  - 实现分层字段验证：基础字段 → 成功状态 → 支付成功
  - 支持v2接口的条件字段验证逻辑
  - 提供详细的字段缺失错误提示
- **PHP 8.0+兼容性**：
  - 修复`libxml_disable_entity_loader`在PHP 8.0+中的弃用警告
  - 添加函数存在性检查，确保向后兼容
  - 优化错误处理和异常捕获
- **创建v2接口测试**：
  - 新增`test_wechat_v2_callback.php`专项测试文件
  - 覆盖v2接口的所有关键特性测试
  - 验证空值过滤、签名算法、XML解析等功能

## 2025-01-23 下午
### 优化订单管理后台显示
- **统一状态显示样式**：
  - 修改订单管理控制器中的状态显示，将`status`和`payment_status`改为使用`using()`和`label()`方法
  - 与`payment_method`保持一致的显示风格，使用标签样式显示不同状态
  - 简化代码结构，移除复杂的`as()`回调函数
  - 保持相同的颜色配置：
    - 订单状态：待付款(default)、待接单(primary)、待师傅上门(info)、待设定费用(warning)、待维修(warning)、待确认(warning)、已完成(success)、已取消(danger)
    - 支付状态：未支付(danger)、部分支付(warning)、已支付(success)、已退款(info)
- **涉及文件**：
  - `app/Admin/Controllers/OrderController.php`（修改第46-87行）

## 2025年1月5日 更新

### 新增功能
1. **订单数量统计接口**
   - 新增 `GET /api/orders/count` 接口
   - 返回待付款、已付款、已取消订单的数量统计
   - 已付款状态包含除待付款和已取消外的所有状态

2. **订单列表搜索功能增强**
   - 完善了 `GET /api/orders` 接口的keyword搜索功能
   - 支持搜索订单号、用户姓名(name)、用户昵称(nickname)、用户手机号(phone)
   - 原有的状态筛选、日期筛选功能保持不变

### 涉及文件
- `app/Http/Controllers/Api/User/OrderController.php` - 新增count方法，完善index方法搜索功能
- `routes/api.php` - 新增订单数量统计路由
- `api_docs.md` - 更新API接口文档

### 技术说明
- 数量统计使用数据库count查询，性能良好
- 搜索使用orWhereHas关联查询，支持模糊匹配
- 保持了现有接口的向后兼容性

### 接口地址
- 订单数量统计：`GET /api/orders/count`
- 订单列表（含搜索）：`GET /api/orders?keyword=关键字`

## 2025-05-29 - 银行管理模块实现

### 更新内容
1. **新增银行模型和管理功能**
   - 创建 `Bank` 模型 (`app/Models/Bank.php`)
   - 实现银行信息的 CRUD 操作
   - 添加银行状态管理和排序功能
   - 提供银行选项和枚举数据获取方法

2. **管理后台银行控制器**
   - 创建 `BankController` (`app/Admin/Controllers/BankController.php`)
   - 实现完整的后台管理界面
   - 支持银行图标上传
   - 银行名称重复验证
   - 使用统计显示和关联银行卡查看

3. **师傅端银行API**
   - 创建师傅端 `BankController` (`app/Http/Controllers/Api/Worker/BankController.php`)
   - 提供银行列表获取接口
   - 提供银行详情查询接口
   - 图标URL自动转换

4. **数据库结构更新**
   - 创建 `banks` 表迁移文件
   - 更新 `worker_bank_cards` 表，添加 `bank_id` 外键
   - 添加必要的索引和约束

5. **WorkerBankCard模型更新**
   - 更新 `WorkerBankCard` 模型
   - 添加与 `Bank` 模型的关联关系
   - 实现银行名称的智能获取方法

6. **路由配置**
   - 添加师傅端银行相关路由：
     - `GET /api/worker/banks` - 获取银行列表
     - `GET /api/worker/banks/{id}` - 获取银行详情

7. **初始数据**
   - 创建 `BankSeeder` 数据填充器
   - 预置21家主要银行数据

### 涉及文件
- `app/Models/Bank.php` - 银行模型（新建）
- `app/Models/WorkerBankCard.php` - 更新关联关系
- `app/Admin/Controllers/BankController.php` - 管理后台控制器（新建）
- `app/Http/Controllers/Api/Worker/BankController.php` - 师傅端API控制器（新建）
- `database/migrations/2025_05_29_212146_create_banks_table.php` - 银行表迁移（新建）
- `database/migrations/2025_05_29_212213_add_bank_id_to_worker_bank_cards_table.php` - 更新银行卡表迁移（新建）
- `database/seeders/BankSeeder.php` - 银行数据填充器（新建）
- `routes/api.php` - 添加师傅端银行路由
- `README.md` - 更新项目文档

### 功能说明
银行管理模块为师傅的银行卡管理和提现功能提供了基础支持。管理员可以在后台维护银行信息，师傅可以通过API获取可用的银行列表来绑定银行卡。系统支持银行图标显示、状态控制和排序管理。

## 2024年12月23日 - 充值功能微信支付集成

### 更新内容
**集成真实微信支付到充值功能**

### 涉及文件
- `app/Http/Controllers/Api/User/RechargeController.php` - 充值控制器重构
- `app/Services/Payment/AbstractPaymentService.php` - 充值支付处理逻辑优化
- `README.md` - 项目文档更新

### 功能说明

#### 1. 充值控制器重构
- **移除模拟支付逻辑**：删除了原有的模拟支付网关和回调处理
- **集成PaymentService**：使用统一的支付服务处理充值支付
- **支持真实微信支付**：通过WechatPaymentService创建真实的微信支付订单
- **完善错误处理**：添加了详细的日志记录和异常处理
- **优化参数验证**：增强了请求参数的验证和错误消息

#### 2. 支付方式扩展
- **微信支付**：支持微信扫码支付，返回二维码链接
- **支付宝支付**：预留支付宝支付接口
- **余额支付**：支持使用钱包余额进行充值（虽然逻辑上不太合理，但为完整性保留）

#### 3. 充值业务流程优化
- **统一支付流程**：充值支付现在与其他支付类型使用相同的处理流程
- **支付回调处理**：充值支付回调由PaymentController统一处理
- **状态同步**：支付成功后自动更新充值状态和用户钱包余额
- **赠送金额处理**：正确处理充值活动中的赠送金额

#### 4. 新增接口
- **充值支付状态查询**：`GET /api/user/recharge/payment-status`
  - 支持查询充值的实时支付状态
  - 自动同步第三方支付状态到本地

#### 5. 抽象支付服务优化
- **充值处理分离**：区分简单充值和完整充值（含赠送金额）
- **数据完整性**：确保充值记录和钱包交易记录的数据一致性
- **日志完善**：增加详细的充值处理日志

#### 6. 支付数据流
```
用户发起充值 → 创建充值记录 → 创建支付记录 → 调用微信支付 → 生成支付二维码 → 用户扫码支付 → 微信支付回调 → 更新支付状态 → 更新充值状态 → 增加钱包余额 → 记录交易日志
```

#### 7. 技术改进
- **代码复用**：充值功能现在复用现有的支付基础设施
- **类型安全**：使用强类型参数和返回值
- **错误恢复**：支持支付状态查询作为回调失败的备用方案
- **数据库事务**：确保充值相关操作的原子性

### 配置要求
- 微信支付商户号配置
- 支付回调URL配置
- 商户证书文件配置

### 测试建议
1. 测试微信支付充值流程
2. 验证充值活动赠送金额计算
3. 检查支付回调处理逻辑
4. 测试异常情况的处理

---

## 历史记录

### 2024年XX月XX日 - 项目初始化
- 完成项目基础架构搭建
- 实现用户认证模块
- 完成订单管理基础功能

### 2024年XX月XX日 - 支付系统开发
- 实现微信支付基础服务
- 完成订单支付功能
- 添加支付回调处理

### 2024年XX月XX日 - 钱包系统开发
- 实现用户钱包功能
- 完成余额支付
- 添加交易记录管理

（更多历史记录...）

## 2025-01-23
### 新增课程权限管理功能
- 创建课程权限管理控制器 `CoursePermissionController`
  - 提供申请观看用户列表展示
  - 支持单个权限申请的审核（通过/拒绝）
  - 支持批量权限申请审核操作
  - 提供详细的筛选和搜索功能
  - 显示用户的其他权限申请记录
- 创建批量操作Action类：
  - `BatchApproveCoursePermission` - 批量通过权限申请
  - `BatchRejectCoursePermission` - 批量拒绝权限申请
- 添加管理后台路由配置：
  - 资源路由：`course-permissions`
  - 审核路由：`course-permissions/{id}/approve` 和 `course-permissions/{id}/reject`
- 创建SQL脚本配置后台菜单和权限：
  - 添加"课程权限管理"菜单项到内容管理模块
  - 配置相关权限（列表、详情、编辑、删除、审核通过、审核拒绝）
  - 将权限分配给超级管理员角色
- 功能特性：
  - 支持AJAX审核操作，无需页面刷新
  - 提供详细的确认提示和操作反馈
  - 遵循Laravel和Dcat Admin最佳实践
  - 完整的事务处理确保数据一致性
  - 涉及文件：
    - `app/Admin/Controllers/CoursePermissionController.php`（新增）
    - `app/Admin/Actions/BatchApproveCoursePermission.php`（新增）
    - `app/Admin/Actions/BatchRejectCoursePermission.php`（新增）
    - `app/Admin/routes.php`（修改）
    - `sql/add_course_permission_menu.sql`（新增）

### 完善课程管理系统
- 解决了课程权限申请审核流程中缺少的管理功能
- 实现了完整的用户申请→管理员审核→权限生效的闭环流程
- 为管理员提供了便捷的批量操作和筛选功能
- 确保了课程访问权限的有效管控

### 课程权限管理跳转功能优化
- **新增课程管理与权限管理之间的快速跳转功能**
  - 在课程管理列表页面添加"权限管理"操作按钮，方便管理员快速查看特定课程的权限申请
  - 实现基于`course_id`参数的自动过滤功能，跳转后只显示当前课程的权限申请记录
  - 在权限管理页面添加课程信息提示区域，显示当前查看的课程标题
  - 新增"返回课程管理"按钮，提供便捷的反向导航
  - 跳转链接使用精确的`course_id`参数，确保过滤准确性
  - 涉及文件：
    - `app/Admin/Controllers/CourseController.php` - 添加权限管理跳转按钮
    - `app/Admin/Controllers/CoursePermissionController.php` - 添加course_id过滤和返回按钮
    - `README.md` - 更新功能说明文档
  - **功能特性**：
    - 在课程列表每行添加蓝色"权限管理"按钮（包含钥匙图标）
    - 点击后自动跳转到对应课程的权限申请列表
    - 权限管理页面顶部显示课程信息提示框
    - 提供"返回课程管理"按钮实现快速返回
    - 优化管理员工作流程，提高操作效率

## 2025-01-23
- 改进师傅年费套餐支付功能，对接真实支付系统
  - 修改 `AnnualPackageController.php` 的 `purchase` 方法，使用 `PaymentService` 创建真实支付订单
  - 在 `Payment` 模型中添加 `TYPE_ANNUAL_PACKAGE` 支付类型常量
  - 在 `PaymentService` 中添加对年费套餐支付类型的验证支持
  - 重构 `paymentCallback` 方法，正确处理支付回调并激活年费套餐
  - 新增 `paymentStatus` 方法，支持前端轮询查询支付状态
  - 统一使用 `ResponseService` 返回格式，提高代码一致性
  - 添加完整的日志记录和错误处理机制
  - 支持微信支付、支付宝支付和余额支付三种支付方式
  - 涉及文件：
    - `app/Http/Controllers/Api/Worker/AnnualPackageController.php`（修改）
    - `app/Models/Payment.php`（修改）
    - `app/Services/PaymentService.php`（修改）

### 修复年费套餐购买功能中的日期处理错误
- 修复 `AnnualPackagePurchase` 模型中的日期字段配置问题
  - 将废弃的 `$dates` 属性改为在 `$casts` 中正确定义日期字段
  - 添加 `start_date`、`end_date`、`created_at`、`updated_at` 的 datetime 转换
  - 在 `$fillable` 中添加缺失的 `order_no` 字段
- 修复 `AnnualPackageController` 中的 clone 错误
  - 将 `(clone $startDate)` 改为使用 `Carbon::parse()` 和 `copy()` 方法
  - 确保日期对象的正确处理，避免 "__clone method called on non-object" 错误
  - 使用更安全的日期处理方式，提高代码健壮性
- 涉及文件：
  - `app/Models/AnnualPackagePurchase.php`（修改）
  - `app/Http/Controllers/Api/Worker/AnnualPackageController.php`（修改）

## 2025-01-23
- 修复年费套餐支付回调处理问题
  - 在`AbstractPaymentService`的`handlePaymentSuccess`方法中添加了对`Payment::TYPE_ANNUAL_PACKAGE`支付类型的处理
  - 新增`handleAnnualPackagePayment`方法，实现年费套餐支付成功后的业务逻辑：
    - 查找对应的年费购买记录
    - 验证购买记录是否存在
    - 检查是否已经激活，避免重复处理
    - 激活年费套餐（将status设置为true）
    - 记录详细的操作日志
  - 修复了年费支付成功后师傅会员信息没有更新的问题
  - 确保支付回调能够正确激活师傅的年费套餐
  - 涉及文件：
    - `app/Services/Payment/AbstractPaymentService.php`（修改）
  - 问题解决：师傅购买年费套餐支付成功后，会员状态现在能够正确更新

## 2025-01-23
- **修复师傅退出申请审核功能问题**
  - 修复 `WorkerQuitRequestController` 中的语法错误和功能缺陷：
    - 恢复被注释的导出状态转换功能，确保导出Excel时状态显示为中文
    - 修复JavaScript中缺少CSRF token的问题，添加`_token: Dcat.token`到所有AJAX请求
    - 改进前端验证逻辑，确保用户输入有效数据（拒绝原因非空、退款金额为有效数字）
    - 添加完整的错误处理，包括成功/失败状态判断和友好的错误提示
  - 优化审核方法的后端处理：
    - 为`approve`、`reject`、`refund`方法添加完整的事务处理
    - 增强状态验证，防止重复操作和无效状态变更
    - 添加详细的参数验证，确保数据完整性
    - 统一返回JSON格式，包含status字段和message信息
    - 添加异常捕获和错误日志记录
  - 添加缺失的数据库相关引用：
    - 在控制器中添加`use Illuminate\Support\Facades\DB;`
  - 涉及文件：
    - `app/Admin/Controllers/WorkerQuitRequestController.php`（修复）
  - **修复的具体问题**：
    - 导出功能状态显示问题（已修复）
    - AJAX请求CSRF验证失败问题（已修复）
    - 审核操作重复执行问题（已修复）
    - 前端错误处理不完整问题（已修复）
    - 后端事务处理缺失问题（已修复）
  - **功能改进**：
    - 审核操作现在更加安全可靠
    - 用户界面交互更加友好
    - 错误提示更加清晰明确
    - 数据完整性得到保障

## 2024年12月19日 - 修复订单取消退款功能

### 问题描述
- 管理员后台取消订单时，退款功能没有正常工作
- 原因：代码中引用了不存在的 `RefundService` 类
- 导致取消订单后用户预约费无法退还

### 解决方案
1. **修复了 OrderController.php 中的退款逻辑**
   - 移除了错误的 `RefundService::refundBookingFee($order)` 调用
   - 实现了完整的退款处理机制

2. **按支付方式分别处理退款**
   - **余额支付**：直接退回到用户钱包余额
     - 更新用户钱包余额
     - 创建退款记录
     - 更新支付记录状态为已退款
     - 创建钱包交易记录
   - **第三方支付**（微信、支付宝）：调用 PaymentService 提交退款申请

3. **完善退款日志记录**
   - 在订单状态日志中详细记录退款处理结果
   - 区分成功、失败和异常情况

### 修改文件
- `app/Admin/Controllers/OrderController.php` - 修复退款逻辑

### 功能验证
- 余额支付的订单取消后，预约费能正确退回到用户余额
- 第三方支付的订单能正确提交退款申请
- 所有退款处理过程都有完整的日志记录

### 注意事项
- 当前系统中只有微信支付服务完整实现，支付宝等其他第三方支付方式需要后续完善
- 余额支付的退款处理是即时的，第三方支付需要等待退款回调

## 2024-12-30
- **修复Dcat Admin控制器display方法参数问题**
- **涉及文件**: `app/Admin/Controllers/OfflineWorkerController.php`
- **修改内容**: 
  - 根据Dcat Admin官方文档规范，修复所有display方法的参数问题
  - 将错误的三参数形式 `($value, $column, $model)` 改为正确的单参数形式
  - 在display回调函数中使用 `$this` 访问当前行的其他字段值，而非传入参数
  - 修复了以下display方法的参数问题：
    - 师傅姓名显示（加粗格式）
    - 所属区域显示（省市区拼接）
    - 服务技能显示（技能名称拼接）
    - 押金信息显示（押金和退回信息）
    - 押金退回日期显示
    - 退回状态显示
  - 修正了属性访问方式，使用 `getAttribute()` 方法确保正确访问模型属性
  - 确保代码符合Dcat Admin 2.x版本的最佳实践
- **技术细节**:
  - 参考文档：https://learnku.com/docs/dcat-admin/2.x/display-and-expansion-of-columns/8091
  - display方法回调函数只接收一个参数（字段值）
  - 通过 `$this` 访问当前行其他字段，如 `$this->field_name` 或 `$this->getAttribute('field_name')`
  - 修复了linter错误，确保代码质量

## 2024-12-20 (下午)
- **修复功能**: 订单派单功能中的代码错误
- **错误类型**: "Call to a member function hasValidAnnualPackage() on string"
- **涉及文件**: 
  - `app/Admin/Controllers/OrderController.php` - 订单管理控制器的 assign 方法
- **问题原因**: 
  - 在筛选有有效年费套餐的师傅时，使用了 `$query->pluck('name', 'id')` 获取师傅数据
  - 该方法返回的是键值对集合，其中值是字符串（师傅姓名）而不是 Worker 模型对象
  - 因此在调用 `$worker->hasValidAnnualPackage()` 时会出错，因为字符串没有该方法
- **修复方案**:
  - 将 `$workers = $query->pluck('name', 'id')` 改为 `$workers = $query->get()`
  - 这样获得的是 Worker 模型对象集合，可以正常调用 `hasValidAnnualPackage()` 方法
  - 在验证年费套餐后，将结果转换为 `$validWorkers->toArray()` 格式返回
- **技术细节**:
  - Worker 模型中已存在 `hasValidAnnualPackage()` 方法，用于检查师傅的年费套餐有效性
  - 该方法检查 AnnualPackagePurchase 表中的状态和结束日期
  - 修复确保了派单功能只显示购买了有效年费套餐的师傅

## 2024-12-31
- **优化离店师傅押金退回流程集成**
- **涉及文件**: 
  - `app/Admin/Controllers/OfflineWorkerController.php` - 离店师傅管理控制器
  - `app/Admin/Controllers/WorkerDepositTransactionController.php` - 押金流水管理控制器
- **修改内容**:
  - 集成 `WorkerDepositService` 到离店师傅管理控制器，确保押金退回操作记录在押金流水系统中
  - 修改 `processRefund` 方法，使用 `depositRefund` 服务方法处理押金退回，自动创建押金流水记录
  - 为每个押金退回操作分配流水号，便于追踪和对账
  - 在离店师傅管理页面添加"押金流水"按钮，方便管理员查看师傅的完整押金流水记录
  - 优化押金流水管理页面，支持从URL参数自动筛选特定师傅的流水记录
  - 改进错误处理机制，确保押金退回失败时回滚所有相关操作
  - 统一操作人记录为当前登录的管理员，提高操作追溯性
- **技术细节**:
  - 使用 `WorkerDepositTransaction::TYPE_REFUND` 类型记录押金退回操作
  - 操作人类型设置为 `OPERATOR_ADMIN`，记录当前管理员ID
  - 支持为两种退店申请类型（WorkerQuitApplication 和 WorkerQuitRequest）处理押金退回
  - 在成功消息中包含流水号，方便管理员记录和核查
  - 通过URL参数支持直接跳转到特定师傅的押金流水记录页面

## 2024年12月19日 - 优惠券模块文本字段功能开发

### 更新内容
为优惠券模块添加了完整的文本字段功能，提升前端显示体验。

### 涉及文件
1. **app/Models/Coupon.php** - 核心模型文件
2. **app/Http/Controllers/Api/User/CouponController.php** - 用户端优惠券控制器
3. **app/Http/Controllers/Api/Marketing/CouponController.php** - 营销端优惠券控制器
4. **app/Http/Controllers/Api/User/PaymentController.php** - 支付控制器
5. **README.md** - 项目文档

### 功能说明

#### 新增模型访问器
在 `Coupon` 模型中添加了以下访问器方法：

1. **getTypeTextAttribute()** - 获取优惠券类型中文描述
   - `all` → `全场通用`
   - `material` → `仅限材料`
   - `service` → `仅限维修服务`

2. **getDiscountTypeTextAttribute()** - 获取折扣类型中文描述
   - `fixed` → `固定金额减免`
   - `percent` → `百分比折扣`

3. **getValidityPeriodTextAttribute()** - 获取有效期格式化文本
   - 自动判断有效期范围并格式化显示
   - 包含过期状态提示

4. **getDiscountValueTextAttribute()** - 获取折扣值格式化文本
   - 固定金额：`减免50元`
   - 百分比：`8折`

5. **getConditionTextAttribute()** - 获取使用条件简短文本
   - 组合类型和金额条件

6. **getConditionTitleAttribute()** - 获取完整条件标题（兼容旧版本）
   - 包含类型、金额条件和有效期的完整描述

#### 控制器更新
- 移除了各控制器中重复的 `formatConditionTitle` 方法
- 更新API返回数据，包含所有新增文本字段
- 保持向后兼容性，保留 `condition_title` 字段

#### 模型配置
- 在 `$appends` 数组中添加了新的访问器属性
- 确保在序列化模型时自动包含这些文本字段

### 技术特点
1. **自动化** - 通过模型访问器自动生成文本，无需手动处理
2. **一致性** - 所有API接口返回统一格式的文本字段
3. **兼容性** - 保持原有 `condition_title` 字段，确保现有功能不受影响
4. **可扩展** - 模型访问器设计便于后续添加更多文本格式化功能

### 使用示例
前端调用优惠券相关API时，会自动获得格式化的文本字段：

```json
{
  "type_text": "全场通用",
  "discount_type_text": "百分比折扣", 
  "discount_value_text": "8折",
  "validity_period_text": "2024-01-01 至 2024-12-31",
  "condition_text": "全场通用，满100元可用",
  "condition_title": "全场通用，满100元可用，2024-01-01至2024-12-31有效"
}
```

### 测试建议
1. 测试不同类型优惠券的文本显示
2. 验证过期优惠券的状态提示
3. 确认固定金额和百分比折扣的文本格式
4. 检查各API接口的返回数据完整性

这次更新大大提升了优惠券模块的用户体验，前端无需再进行复杂的文本格式化处理。

## 2025年1月21日 - 微信登录集成功能开发

### 更新内容概述
完成了微信登录集成和用户兼容功能的开发，支持静默登录和授权登录两种模式，实现了多平台用户账号合并机制。

### 详细更新内容

#### 1. 数据库结构优化
**涉及文件**: 
- `database/migrations/2025_01_21_000003_create_user_merge_logs_table.php`

**功能说明**: 
- 创建用户合并日志表，记录账号合并操作的详细信息
- 支持不同合并类型：手机号合并到微信、微信合并到手机号、重复账号合并
- 记录合并状态、操作者信息、错误信息等

#### 2. 数据模型扩展
**涉及文件**: 
- `app/Models/WechatLoginLog.php`
- `app/Models/UserMergeLog.php`

**功能说明**: 
- **WechatLoginLog模型**: 记录微信登录日志，包括登录类型、平台、结果等信息
- **UserMergeLog模型**: 管理用户合并操作，提供合并状态跟踪和错误处理

#### 3. 微信配置完善
**涉及文件**: 
- `config/wechat.php`

**功能说明**: 
- 新增微信登录配置部分，支持小程序、APP、H5三种平台
- 添加通用配置：超时时间、重试次数、缓存前缀等
- 为不同平台配置独立的AppID和Secret

#### 4. 核心服务类开发
**涉及文件**: 
- `app/Services/WechatLoginService.php`

**功能说明**: 
- **静默登录**: 使用微信授权码获取OpenID，适用于已注册用户快速登录
- **授权登录**: 获取用户基本信息，支持新用户注册流程
- **手机号绑定**: 处理微信用户与手机号的绑定和账号合并
- **用户匹配**: 通过UnionID和OpenID智能匹配用户
- **安全机制**: 防止授权码重复使用、API请求重试、错误日志记录

#### 5. 控制器功能扩展
**涉及文件**: 
- `app/Http/Controllers/Api/User/AuthController.php`

**功能说明**: 
- **wechatSilentLogin**: 微信静默登录接口
- **wechatAuthorizedLogin**: 微信授权登录接口
- **wechatBindPhone**: 微信绑定手机号接口
- 集成WechatLoginService服务，提供统一的响应格式
- 支持不同登录状态的处理（成功、需要授权、需要绑定手机号等）

#### 6. API路由配置
**涉及文件**: 
- `routes/api.php`

**功能说明**: 
- 新增三个微信登录相关路由：
  - `POST /api/user/wechat/silent-login` - 静默登录
  - `POST /api/user/wechat/authorized-login` - 授权登录
  - `POST /api/user/wechat/bind-phone` - 绑定手机号
- 所有新接口均为公开接口，无需认证

#### 7. 接口文档编写
**涉及文件**: 
- `docs/api/user/wechat_login_api.md`

**功能说明**: 
- 详细的API接口文档，包含请求参数、响应格式、错误码说明
- 提供完整的前端集成示例（小程序代码）
- 包含登录流程图和最佳实践建议
- 配置要求和测试建议

### 技术特点

#### 1. 用户账号合并策略
- 以手机号为最终用户唯一标识
- 智能处理多平台用户账号冲突
- 支持数据迁移和账号状态管理

#### 2. 安全机制
- 微信授权码防重放攻击（5分钟缓存）
- 短信验证码时效控制
- 完整的操作日志记录

#### 3. 多平台支持
- 支持微信小程序、APP、H5三种平台
- 通过UnionID实现跨平台用户识别
- 平台独立的配置管理

#### 4. 错误处理
- 完善的异常捕获和日志记录
- 用户友好的错误提示
- 支持不同错误场景的处理

### 待完成工作

1. **数据库迁移**: 需要运行迁移命令创建新表结构
2. **环境配置**: 需要在.env文件中配置微信相关参数
3. **测试验证**: 需要在微信开发者工具中测试完整流程
4. **用户钱包**: 确保新注册用户自动创建钱包记录

### 使用说明

#### 环境配置
在`.env`文件中添加以下配置：
```env
# 微信小程序
WECHAT_MINIPROGRAM_APPID=your_miniprogram_appid
WECHAT_MINIPROGRAM_SECRET=your_miniprogram_secret

# 微信APP（如需要）
WECHAT_APP_APPID=your_app_appid
WECHAT_APP_SECRET=your_app_secret

# 微信H5（如需要）
WECHAT_H5_APPID=your_h5_appid
WECHAT_H5_SECRET=your_h5_secret
```

#### 前端集成
参考`docs/api/user/wechat_login_api.md`文档中的JavaScript示例代码，在小程序中集成登录功能。

### 影响范围
- 新增功能，不影响现有登录方式
- 兼容现有用户数据结构
- 可通过配置开关控制功能启用

### 备注
本次更新实现了完整的微信登录集成方案，为用户提供了更便捷的登录体验，同时保证了数据的一致性和安全性。

## 2025年1月21日 - 师傅端微信登录功能开发

### 更新内容概述
完成了师傅端微信登录功能的完整开发，包括静默登录、授权登录、绑定注册和绑定手机号四个核心功能，针对师傅端的特殊需求进行了专门适配。

### 详细更新内容

#### 1. 数据库结构扩展
**新增文件**: 
- `database/migrations/2025_01_21_000004_add_wechat_fields_to_workers_table.php`
- `database/migrations/2025_01_21_000005_add_worker_id_to_wechat_login_logs_table.php`

**功能说明**: 
- 为workers表添加微信相关字段：unionid、session_key、wechat_nickname、wechat_avatar、platform、last_login_platform、last_login_at
- 扩展wechat_login_logs表，添加worker_id字段支持师傅端日志记录
- 添加必要的索引和外键约束

#### 2. 数据模型更新
**修改文件**: 
- `app/Models/Worker.php`
- `app/Models/WechatLoginLog.php`

**功能说明**: 
- **Worker模型**: 添加微信相关字段到fillable和casts数组
- **WechatLoginLog模型**: 添加师傅关联关系，支持师傅端日志记录

#### 3. 师傅端微信登录服务
**新增文件**: 
- `app/Services/WechatWorkerLoginService.php`

**功能说明**: 
- **静默登录**: 已绑定师傅可直接登录
- **授权登录**: 获取微信用户信息，检查绑定状态
- **绑定注册**: 新师傅通过微信完成注册流程，包含实名认证和技能认证
- **绑定手机号**: 已登录师傅绑定/更新手机号
- **特殊处理**: 师傅注册需要完整的实名认证信息，包括身份证、技能证书等
- **审核流程**: 注册后需要等待审核，提供相应的状态提示

#### 4. 控制器功能扩展
**修改文件**: 
- `app/Http/Controllers/Api/Worker/AuthController.php`

**功能说明**: 
- **wechatSilentLogin**: 师傅微信静默登录接口
- **wechatAuthorizedLogin**: 师傅微信授权登录接口
- **wechatBindRegister**: 师傅微信绑定注册接口
- **wechatBindPhone**: 师傅微信绑定手机号接口
- 集成WechatWorkerLoginService服务
- 完善参数验证，包含师傅注册所需的所有字段验证
- 处理师傅审核状态的不同响应

#### 5. API路由配置
**修改文件**: 
- `routes/api.php`

**功能说明**: 
- 新增师傅端微信登录路由组：
  - `POST /api/worker/wechat/silent-login` - 静默登录
  - `POST /api/worker/wechat/authorized-login` - 授权登录
  - `POST /api/worker/wechat/bind-register` - 绑定注册
  - `POST /api/worker/wechat/bind-phone` - 绑定手机号（需要认证）
- 更新中间件配置，区分公开接口和需要认证的接口

#### 6. 完整文档体系
**新增文件**: 
- `师傅端微信登录设计文档.md` - 详细设计文档
- `docs/api/worker/wechat_login_api.md` - API接口文档
- `docs/api/worker/wechat_login_frontend_example.md` - 前端集成示例

**功能说明**: 
- **设计文档**: 包含系统架构、数据库设计、安全考虑、用户体验设计
- **API文档**: 详细的接口规范，包含所有请求参数、响应格式、错误码说明
- **前端示例**: 完整的微信小程序集成代码，包含页面结构、逻辑处理、样式设计

### 师傅端特殊功能

#### 1. 实名认证流程
- 师傅注册需要提供真实姓名、身份证号、身份证照片
- 身份证号和手机号具有唯一性验证
- 微信账号只能绑定一个师傅账号

#### 2. 技能认证系统
- 师傅需要选择服务技能类型
- 必须上传技能证书图片
- 技能信息需要通过审核

#### 3. 服务区域配置
- 师傅需要设置服务区域（省市区）
- 支持多个服务区域管理
- 区域信息用于订单派发

#### 4. 审核状态管理
- 新注册师傅状态为"待审核"
- 登录时返回审核状态信息
- 不同审核状态有相应的功能限制

### 技术特点

#### 1. 服务复用设计
- 复用用户端WechatLoginService的微信API调用部分
- 针对师傅端需求进行专门适配
- 保持代码结构的一致性

#### 2. 数据完整性
- 完整的事务处理，确保数据一致性
- 详细的错误处理和回滚机制
- 全面的日志记录

#### 3. 安全机制
- 防止恶意注册和虚假信息
- 微信授权码防重复使用
- 完整的参数验证和错误处理

#### 4. 用户体验
- 师傅首次使用需要完整注册流程，但可以预填微信信息
- 已注册师傅可以快速登录
- 审核状态的师傅登录后有相应提示
- 支持手机号登录和微信登录两种方式

### 与用户端的区别

| 功能 | 用户端 | 师傅端 |
|------|--------|--------|
| 注册门槛 | 手机号验证即可 | 需要实名认证+技能认证 |
| 审核流程 | 无需审核 | 注册后需要等待审核 |
| 必填信息 | 手机号、昵称 | 姓名、身份证、技能证书、服务区域 |
| 账号状态 | 注册即可使用 | 审核通过后才能完整使用 |
| 绑定限制 | 一个微信可绑定一个用户 | 一个微信只能绑定一个师傅 |

### 部署要求

#### 1. 数据库迁移
```bash
php artisan migrate
```

#### 2. 环境配置
确保.env文件中有微信小程序配置：
```env
WECHAT_MINIPROGRAM_APPID=your_miniprogram_appid
WECHAT_MINIPROGRAM_SECRET=your_miniprogram_secret
```

#### 3. 权限配置
确保师傅端小程序已配置获取用户信息和手机号的权限

### 测试建议

1. **完整注册流程测试**
   - 测试新师傅通过微信注册的完整流程
   - 验证所有必填字段的验证逻辑
   - 测试图片上传和表单提交

2. **登录流程测试**
   - 测试已注册师傅的静默登录
   - 测试授权登录流程
   - 验证不同审核状态的响应

3. **边界情况测试**
   - 测试重复注册的处理
   - 测试微信账号已绑定的情况
   - 测试手机号冲突的处理

4. **安全性测试**
   - 测试授权码重复使用的防护
   - 验证参数验证的完整性
   - 测试异常情况的错误处理

### 影响范围
- 新增功能，不影响现有师傅登录方式
- 兼容现有师傅数据结构
- 为师傅端提供更便捷的登录体验

### 备注
师傅端微信登录功能充分考虑了师傅端的特殊业务需求，在保证用户体验的同时，确保了平台的安全性和数据的完整性。该功能为师傅提供了更便捷的注册和登录方式，同时保持了严格的实名认证和技能认证流程。

## 2025年1月21日 - 师傅端微信绑定注册接口优化

### 更新内容
1. **优化师傅端微信绑定注册逻辑**
   - 移除密码字段要求，微信注册不再需要密码
   - 移除手机号唯一性验证限制
   - 实现智能账号处理机制

2. **新增账号合并功能**
   - 如果手机号已注册但未绑定微信：自动执行账号合并操作
   - 如果手机号已注册且已绑定微信：直接执行登录流程
   - 合并过程会保留现有师傅信息，只更新微信相关字段

3. **完善合并日志记录**
   - 所有合并操作都会记录到 `user_merge_logs` 表
   - 记录详细的合并前后数据对比
   - 支持合并状态跟踪（处理中、已完成、失败）

### 涉及文件
- `app/Http/Controllers/Api/Worker/AuthController.php` - 修改wechatBindRegister方法验证规则
- `app/Services/WechatWorkerLoginService.php` - 重构bindRegister方法逻辑
- `docs/api/worker/wechat_login_api.md` - 更新API文档

### 功能说明
- **新用户注册**：手机号未注册时创建新师傅账号，生成随机密码
- **账号合并**：手机号已注册但未绑定微信时，将微信信息绑定到现有账号
- **直接登录**：手机号已注册且已绑定微信时，直接登录现有账号
- **数据保护**：合并时不会删除现有技能，只会添加新技能
- **审核状态**：新注册师傅状态为待审核，合并后保持原有审核状态

### 技术改进
- 使用事务确保数据一致性
- 完善错误处理和日志记录
- 支持多种响应状态（注册、合并、登录）
- 优化代码结构，提高可维护性

## 2024年12月28日 - 微信登录流程逻辑问题修复

### 问题发现
用户指出了师傅端微信登录流程中的一个重要逻辑问题：
> "bindPhone 这个接口需要登录的话，那我授权登录也没有返回token啊"

### 问题分析
1. **第一步 `wechatAuthorizedLogin`**：如果师傅不存在，返回 `need_phone` 状态，但没有返回 token
2. **第二步 `wechatBindPhone`**：用户误以为需要 token 验证，形成逻辑矛盾
3. **设计缺陷**：如果第一步没有返回 token，前端无法进行后续身份验证

### 解决方案
1. **明确设计原则**：
   - 所有微信相关的登录/注册接口都不需要 token 验证
   - 安全性通过微信的授权码(code)和 OpenID 来保证
   - 避免循环依赖，简化登录流程

2. **中间件配置确认**：
   - 确认三步微信登录接口都在 `except` 列表中
   - 不需要 token 验证：`wechatAuthorizedLogin`、`wechatBindPhone`、`wechatCompleteProfile`

### 更新内容
1. **文档优化**：
   - 更新 `docs/api/worker/auth.md`，详细说明微信登录流程设计原则
   - 明确指出三步流程都不需要 token 验证的原因
   - 提供完整的前端调用示例

2. **代码优化**：
   - 添加 `wechatBindRegister` 方法，保持向后兼容
   - 完善注释和说明

3. **新增文档**：
   - 创建 `师傅端微信登录三步流程优化说明.md`
   - 详细解释问题原因、解决方案和安全机制

### 涉及文件
- `app/Http/Controllers/Api/Worker/AuthController.php` - 添加向后兼容方法
- `docs/api/worker/auth.md` - 完全重写，明确流程设计
- `师傅端微信登录三步流程优化说明.md` - 新增详细说明文档

### 功能说明
- **安全机制**：通过微信授权码、OpenID 唯一性、时效性限制等保证安全
- **用户体验**：流程顺畅，无需中断，避免循环依赖
- **向后兼容**：保留旧接口，不影响现有系统

### 优化效果
1. **逻辑清晰**：三步流程都不需要 token，避免循环依赖
2. **安全可靠**：通过微信授权机制保证安全性
3. **用户体验**：流程顺畅，无需中断
4. **文档完善**：提供详细的设计说明和使用示例

## 2024年12月28日 - 师傅端微信绑定注册接口优化

### 更新内容
1. **优化师傅端微信绑定注册流程**：
   - 移除密码字段要求，微信注册不再需要密码
   - 实现智能账号处理机制，支持账号合并和直接登录
   - 新增账号合并功能，记录详细合并日志

2. **涉及文件**：
   - `app/Http/Controllers/Api/Worker/AuthController.php` - 更新中间件配置
   - `app/Services/WechatWorkerLoginService.php` - 新增三步登录流程方法
   - `docs/api/worker/auth.md` - 更新API文档
   - `routes/api.php` - 新增路由

3. **新增功能**：
   - 三步微信登录流程：授权登录 → 绑定手机号 → 完善师傅信息
   - 智能账号合并：自动处理手机号已注册但未绑定微信的情况
   - 详细日志记录：记录所有微信登录和账号合并操作

### 功能说明
- **第一步**：微信授权登录，获取基本信息
- **第二步**：绑定手机号，支持账号合并
- **第三步**：完善师傅注册信息，创建账号

## 2024年12月27日 - 师傅端订单管理功能优化

### 更新内容
1. **新增师傅端订单状态统计接口**：
   - 统计各状态订单数量
   - 提供订单状态分布信息
   - 支持缓存优化性能

2. **优化师傅端订单列表接口**：
   - 支持多状态筛选
   - 优化查询性能
   - 完善订单信息返回

3. **涉及文件**：
   - `app/Http/Controllers/Api/Worker/OrderController.php` - 新增统计接口
   - `docs/api/worker/order.md` - 更新API文档
   - `routes/api.php` - 新增路由

### 功能说明
- 师傅可以查看各状态订单的数量统计
- 支持按订单状态筛选订单列表
- 提供完整的订单信息展示

## 2024年12月26日 - 用户端公告大厅功能开发

### 更新内容
1. **新增公告大厅前端页面**：
   - 实现公告列表展示
   - 支持分页加载
   - 优化用户体验

2. **完善公告管理API**：
   - 优化公告列表接口
   - 新增公告详情接口
   - 完善API文档

3. **涉及文件**：
   - `app/Http/Controllers/Api/Client/ContentController.php` - 公告接口
   - `docs/api/client/content.md` - API文档
   - `docs/api/public_hall_frontend_example.md` - 前端示例

### 功能说明
- 用户可以查看平台发布的公告信息
- 支持公告列表和详情查看
- 提供完整的前端实现示例

## 2024年12月25日 - 项目初始化

### 项目概述
本项目是一个提供本地维修、家政等服务的平台，包含用户端和师傅端两个应用，以及管理后台。

### 技术架构
- **后端**：PHP 8.1+ + Laravel 10.x + Dcat Admin 2.x
- **数据库**：MySQL 8.0+
- **前端**：Vue 3 + Uniapp（小程序端）
- **API设计**：RESTful API

### 主要功能模块
1. 用户认证模块 - 用户注册、登录、个人信息管理
2. 服务管理模块 - 服务项目管理、分类和展示
3. 订单管理模块 - 订单创建、派单、状态流转
4. 支付模块 - 支付管理、退款管理、充值功能
5. 师傅管理模块 - 师傅注册认证、接单管理
6. 材料管理模块 - 材料商城、购物车、材料订单
7. 内容管理模块 - 招聘信息、课程管理
8. 营销管理模块 - 营销活动、优惠券、会员套餐
9. 统计分析模块 - 各类数据统计和分析
10. 系统管理模块 - 权限管理、参数配置

### 应用类型
1. **用户端** - 小程序/H5应用，用户下单和管理订单
2. **师傅端** - 小程序/H5应用，师傅接单和服务管理
3. **管理后台** - Dcat Admin，平台运营管理

### 开发规范
- 遵循 Laravel 和 PHP 最佳实践
- 使用 RESTful API 设计规范
- 实现完整的订单流程管理
- 集成支付功能
- 多角色用户系统（用户、师傅、管理员）
