# 本地服务平台 API 文档

## 目录

1. [用户端订单接口](#用户端订单接口)
2. [师傅端订单接口](#师傅端订单接口)

## 全局说明

### 接口返回格式

所有API接口统一返回JSON格式：

```json
{
  "code": 0,       // 0表示成功，非0表示失败
  "message": "success",
  "data": { ... }  // 返回数据，具体结构根据接口不同
}
```

### 订单状态说明

| 状态码 | 状态说明 |
|-------|---------|
| 0 | 待付款 (STATUS_PENDING_PAYMENT) |
| 1 | 待接单 (STATUS_PENDING_ACCEPT) |
| 2 | 待师傅上门 (STATUS_PENDING_VISIT) |
| 3 | 待设定服务费用 (STATUS_PENDING_FEE) |
| 4 | 待维修 (STATUS_PENDING_SERVICE) |
| 5 | 待维修确认 (STATUS_PENDING_CONFIRM) |
| 6 | 已完成 (STATUS_COMPLETED) |
| 7 | 已取消 (STATUS_CANCELLED) |

### 支付方式说明

| 支付方式 | 说明 |
|---------|-----|
| wechat | 微信支付 |
| alipay | 支付宝支付 |
| balance | 余额支付 |

### 支付类型说明

| 支付类型 | 说明 |
|---------|-----|
| booking | 预约费 |
| service | 服务费 |
| material | 材料费 |
| recharge | 充值 |

### 支付状态说明

| 状态码 | 状态说明 |
|-------|---------|
| 0 | 待支付 (STATUS_PENDING) |
| 1 | 已支付 (STATUS_PAID) |
| 2 | 已退款 (STATUS_REFUNDED) |

---

## 用户端订单接口

### 1. 创建订单

- **接口说明**: 用户创建新的维修订单
- **请求方式**: POST
- **请求路径**: `/api/orders`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| service_id | integer | 是 | 服务ID |
| address_id | integer | 是 | 用户地址ID |
| appointment_date | date | 是 | 预约日期（格式：YYYY-MM-DD，必须大于等于当天日期） |
| appointment_time | string | 是 | 预约时间段（如：09:00-10:00） |
| payment_method | string | 是 | 支付方式（wechat/alipay/balance） |
| remark | string | 否 | 订单备注，最多500字 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order_id": 123,
    "order_no": "ORD202307251234567890",
    "booking_fee": "50.00",
    "payment_url": "/api/orders/123/pay-booking"
  }
}
```

### 2. 获取订单列表

- **接口说明**: 获取当前用户的订单列表
- **请求方式**: GET
- **请求路径**: `/api/orders`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| status | string | 否 | 订单状态筛选（pending_payment/pending_service/completed/cancelled/all） |
| start_date | date | 否 | 开始日期筛选（格式：YYYY-MM-DD） |
| end_date | date | 否 | 结束日期筛选（格式：YYYY-MM-DD） |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页记录数，默认10 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 123,
        "order_no": "ORD202307251234567890",
        "service_name": "家电维修",
        "service_image": "https://example.com/image.jpg",
        "booking_fee": "50.00",
        "service_fee": "100.00",
        "total_fee": "150.00",
        "status": 1,
        "status_text": "待服务",
        "created_at": "2023-07-25 12:34:56",
        "show_confirm_material_used_button": false,
        "show_pay_material_fee_button": false,
        "show_pay_service_fee_button": true,
        "show_confirm_repair_completed_button": false
      }
    ],
    "first_page_url": "http://example.com/api/orders?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/orders?page=1",
    "links": [...],
    "next_page_url": null,
    "path": "http://example.com/api/orders",
    "per_page": 10,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### 3. 获取订单详情

- **接口说明**: 获取指定订单的详情信息
- **请求方式**: GET
- **请求路径**: `/api/orders/{id}`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "order": {
      "id": 123,
      "order_no": "ORD202307251234567890",
      "service_id": 456,
      "service_name": "家电维修",
      "service_image": "https://example.com/image.jpg",
      "service_category_id": 789,
      "service_category_name": "家电",
      "service_description": "提供各类家电维修服务",
      "booking_fee": "50.00",
      "service_fee": "100.00",
      "total_fee": "150.00",
      "deposit_fee": "0.00",
      "area_id": 10,
      "area_name": "朝阳区",
      "address": "北京市朝阳区xxx街道xxx小区",
      "contact_name": "张三",
      "contact_phone": "13800138000",
      "appointment_date": "2023-07-25",
      "appointment_time": "09:00-10:00",
      "status": 1,
      "status_text": "待服务",
      "detail_status_text": "等接单",
      "remark": "门牌号1001",
      "payment_method": "wechat",
      "payment_method_text": "微信支付",
      "booking_paid_at": "2023-07-25 12:34:56",
      "service_paid_at": null,
      "worker_arrived_at": null,
      "service_fee_set_at": null,
      "verified_at": null,
      "completed_at": null,
      "created_at": "2023-07-25 12:34:56",
      "process_timeline": [...]
    },
    "show_confirm_material_used_button": false,
    "show_pay_material_fee_button": false,
    "show_pay_service_fee_button": true,
    "show_confirm_repair_completed_button": false,
    "worker": {
      "id": 789,
      "name": "李师傅",
      "avatar": "https://example.com/avatar.jpg",
      "phone": "13900139000",
      "level": "高级技师",
      "rating": 4.8
    },
    "verification": {
      "code": "ABCD1234",
      "qrcode": "/api/orders/123/verification-qrcode",
      "qrcode_base64": "data:image/png;base64,..."
    },
    "construction_images": [
      {
        "id": 1,
        "image_url": "https://example.com/construction1.jpg",
        "created_at": "2023-07-25 14:00:00"
      }
    ],
    "material_orders": [...],
    "payments": [...],
    "review": {
      "id": 101,
      "rating": 5,
      "content": "服务很好，师傅专业",
      "images": ["https://example.com/review1.jpg"],
      "created_at": "2023-07-26 10:00:00"
    }
  }
}
```

### 4. 取消订单

- **接口说明**: 用户取消订单
- **请求方式**: POST
- **请求路径**: `/api/orders/{id}/cancel`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| cancel_reason | string | 否 | 取消原因 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "订单取消成功",
  "data": null
}
```

### 5. 支付预约费用

- **接口说明**: 支付订单预约费用
- **请求方式**: POST
- **请求路径**: `/api/orders/{id}/pay-booking`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| payment_method | string | 是 | 支付方式（wechat/alipay/balance） |
| coupon_id | integer | 否 | 优惠券ID |

- **响应示例**:

```json
{
  "code": 200,
  "message": "支付成功",
  "data": {
    "payment_result": true,
    "order_id": 123,
    "order_status": 1,
    "order_status_text": "等接单"
  }
}
```

### 6. 支付服务费用

- **接口说明**: 支付订单服务费用
- **请求方式**: POST
- **请求路径**: `/api/orders/{id}/pay-service`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| payment_method | string | 是 | 支付方式（wechat/alipay/balance） |
| coupon_id | integer | 否 | 优惠券ID |
| pay_type | string | 是 | 支付类型（full：全额支付，deposit：支付定金） |

- **响应示例**:

```json
{
  "code": 200,
  "message": "支付成功",
  "data": {
    "payment_result": true,
    "order_id": 123,
    "order_status": 4,
    "order_status_text": "待维修"
  }
}
```

### 7. 确认维修完成

- **接口说明**: 用户确认维修已完成
- **请求方式**: POST
- **请求路径**: `/api/orders/{id}/complete`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "维修完成确认成功",
  "data": {
    "order_id": 123,
    "order_status": 6,
    "order_status_text": "已完成"
  }
}
```

### 8. 订单评价

- **接口说明**: 用户对已完成订单提交评价
- **请求方式**: POST
- **请求路径**: `/api/orders/{id}/review`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| rating | integer | 是 | 评分（1-5星） |
| content | string | 是 | 评价内容，最多500字 |
| images | array | 否 | 评价图片URL数组 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": {
    "review_id": 101,
    "order_id": 123
  }
}
```

### 9. 获取评论列表

- **接口说明**: 获取当前用户的所有评论
- **请求方式**: GET
- **请求路径**: `/api/orders/reviews`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页记录数，默认10，最大50 |
| service_id | integer | 否 | 按服务ID筛选 |
| worker_id | integer | 否 | 按师傅ID筛选 |
| status | integer | 否 | 按状态筛选(0=待审核，1=已通过，2=已拒绝) |
| rating | integer | 否 | 按评分筛选(1-5星) |

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "order_id": 100,
        "order_no": "ORD202306010001",
        "service_id": 2,
        "service_name": "家电维修",
        "service_image": "https://example.com/images/service1.jpg",
        "worker_id": 10,
        "worker_name": "张师傅",
        "worker_avatar": "https://example.com/images/worker1.jpg",
        "rating": 5,
        "content": "师傅服务很专业，维修速度快，很满意！",
        "images": [
          "https://example.com/images/review1.jpg",
          "https://example.com/images/review2.jpg"
        ],
        "status": 1,
        "status_text": "已通过",
        "created_at": "2023-06-01 14:30:00"
      }
    ],
    "first_page_url": "http://example.com/api/orders/reviews?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://example.com/api/orders/reviews?page=5",
    "links": [...],
    "next_page_url": "http://example.com/api/orders/reviews?page=2",
    "path": "http://example.com/api/orders/reviews",
    "per_page": 10,
    "prev_page_url": null,
    "to": 10,
    "total": 45
  }
}
```

### 10. 获取订单状态流转记录

- **接口说明**: 获取订单状态变更历史记录
- **请求方式**: GET
- **请求路径**: `/api/orders/{id}/status-logs`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 201,
      "previous_status": 0,
      "previous_status_text": "待付款",
      "previous_simple_status": "待付款",
      "current_status": 1,
      "current_status_text": "等接单",
      "current_simple_status": "待服务",
      "operator_type": "user",
      "operator_name": "张三",
      "remark": "用户支付预约费用",
      "created_at": "2023-07-25 12:34:56"
    }
  ]
}
```

### 11. 删除订单

- **接口说明**: 删除已完成或已取消的订单
- **请求方式**: DELETE
- **请求路径**: `/api/orders/{id}`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "订单删除成功",
  "data": null
}
```

### 获取订单数量统计
**接口地址：** `GET /api/worker/material-orders/count`  
**需要认证：** 是  
**请求参数：** 无  

**返回示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "pending_payment": 2,  // 待付款订单数量
    "paid": 5,            // 已付款订单数量（包含所有已付款状态）
    "cancelled": 1        // 已取消订单数量
  }
}
```

### 获取订单列表
**接口地址：** `GET /api/orders`  
**需要认证：** 是  
**请求参数：**
- `status` (可选): 订单状态筛选
  - `pending_payment` - 待付款
  - `pending_service` - 待服务（包含待接单、待上门、待维修等状态）
  - `completed` - 已完成
  - `cancelled` - 已取消
  - `all` - 全部（默认）
- `start_date` (可选): 开始日期 (YYYY-MM-DD)
- `end_date` (可选): 结束日期 (YYYY-MM-DD)
- `keyword` (可选): 关键字搜索，支持搜索：
  - 订单号
  - 用户姓名(name)
  - 用户昵称(nickname)  
  - 用户手机号(phone)
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10

**返回示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "order_no": "ORD202501051234567890",
        "service_name": "空调维修",
        "service_image": "image_url",
        "booking_fee": "50.00",
        "service_fee": "120.00",
        "total_fee": "170.00",
        "status": 1,
        "status_text": "待接单",
        "created_at": "2025-01-05 12:34:56",
        // ... 其他字段
      }
    ],
    "total": 10,
    "per_page": 10,
    "current_page": 1,
    "last_page": 1
  }
}
```

---

## 师傅端订单接口

### 1. 获取订单列表

- **接口说明**: 获取师傅名下的订单列表
- **请求方式**: GET
- **请求路径**: `/api/worker/orders`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| type | string | 否 | 订单类型筛选（pending_accept/pending_service/pending_confirm/completed/all） |
| start_date | date | 否 | 开始日期筛选（格式：YYYY-MM-DD） |
| end_date | date | 否 | 结束日期筛选（格式：YYYY-MM-DD） |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页记录数，默认10 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 10,
    "status_counts": {
      "pending_accept": 2,
      "pending_service": 3,
      "pending_confirm": 1,
      "completed": 4,
      "all": 10
    },
    "list": [
      {
        "id": 123,
        "order_no": "ORD202307251234567890",
        "service_id": 456,
        "booking_fee": "50.00",
        "service_fee": "100.00",
        "total_fee": "150.00",
        "status": 1,
        "status_text": "待接单",
        "created_at": "2023-07-25 12:34:56",
        "service_name": "家电维修",
        "show_set_service_fee_button": false,
        "show_change_service_fee_button": false,
        "show_verify_button": false,
        "show_select_material_button": false,
        "show_confirm_material_used_button": false,
        "show_pay_material_fee_button": false,
        "show_confirm_repair_completed_button": false,
        "show_upload_construction_image_button": false,
        "show_upload_completion_image_button": false
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total_pages": 1
    }
  }
}
```

### 2. 获取订单详情

- **接口说明**: 获取指定订单的详情信息
- **请求方式**: GET
- **请求路径**: `/api/worker/orders/{id}`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "order": {
      "id": 123,
      "order_no": "ORD202307251234567890",
      "service_id": 456,
      "service_name": "家电维修",
      "service_image": "https://example.com/image.jpg",
      "service_category_id": 789,
      "service_category_name": "家电",
      "service_description": "提供各类家电维修服务",
      "booking_fee": "50.00",
      "service_fee": "100.00",
      "total_fee": "150.00",
      "deposit_fee": "0.00",
      "area_id": 10,
      "area_name": "朝阳区",
      "address": "北京市朝阳区xxx街道xxx小区",
      "contact_name": "张三",
      "contact_phone": "13800138000",
      "appointment_date": "2023-07-25",
      "appointment_time": "09:00-10:00",
      "status": 1,
      "status_text": "待接单",
      "detail_status_text": "待接单",
      "remark": "门牌号1001",
      "payment_method": "wechat",
      "payment_method_text": "微信支付",
      "booking_paid_at": "2023-07-25 12:34:56",
      "service_paid_at": null,
      "worker_arrived_at": null,
      "service_fee_set_at": null,
      "verified_at": null,
      "completed_at": null,
      "created_at": "2023-07-25 12:34:56",
      "process_timeline": [...]
    },
    "worker": {
      "id": 789,
      "name": "李师傅",
      "avatar": "https://example.com/avatar.jpg",
      "phone": "13900139000",
      "level": "高级技师",
      "rating": 4.8
    },
    "verification": {
      "code": "ABCD1234",
      "qrcode": "/api/orders/123/verification-qrcode"
    },
    "construction_images": [...],
    "material_images": [...],
    "completion_images": [...],
    "material_orders": [...],
    "payments": [...],
    "review": {
      "id": 101,
      "rating": 5,
      "content": "服务很好，师傅专业",
      "images": ["https://example.com/review1.jpg"],
      "created_at": "2023-07-26 10:00:00"
    },
    "show_set_service_fee_button": false,
    "show_change_service_fee_button": false,
    "show_verify_button": false,
    "show_select_material_button": false,
    "show_confirm_material_used_button": false,
    "show_pay_material_fee_button": false,
    "show_confirm_repair_completed_button": false,
    "show_upload_construction_image_button": false,
    "show_upload_completion_image_button": false
  }
}
```

### 3. 师傅接单

- **接口说明**: 师傅接受订单
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/accept`
- **请求参数**: 无

- **响应示例**:

```json
{
  "code": 200,
  "message": "接单成功",
  "data": {
    "order_id": 123,
    "order_status": 2,
    "order_status_text": "待师傅上门"
  }
}
```

### 4. 师傅到达确认

- **接口说明**: 师傅到达用户地点后确认到达
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/arrive`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| verification_code | string | 是 | 用户提供的核销码 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "到达确认成功",
  "data": {
    "order_id": 123,
    "order_status": 3,
    "order_status_text": "待设定服务费用"
  }
}
```

### 5. 设定服务费用

- **接口说明**: 师傅设定维修服务费用
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/set-service-fee`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| service_fee | decimal | 是 | 服务费用金额 |
| is_deposit_payment | integer | 是 | 是否需要支付定金（0：不需要，1：需要） |
| deposit_fee | decimal | 条件必填 | 定金金额（当is_deposit_payment=1时必填） |
| remark | string | 否 | 费用说明备注 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "服务费用设定成功",
  "data": {
    "order_id": 123,
    "service_fee": "100.00",
    "is_deposit_payment": 1,
    "deposit_fee": "30.00",
    "order_status": 4,
    "order_status_text": "待维修"
  }
}
```

### 6. 上传施工图片

- **接口说明**: 上传施工过程图片
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/upload-construction-images`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| images | array | 是 | 图片URL数组，最多上传6张 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "图片上传成功",
  "data": {
    "images": [
      {
        "id": 1,
        "image_url": "https://example.com/construction1.jpg",
        "created_at": "2023-07-25 14:00:00"
      }
    ]
  }
}
```

### 7. 上传材料使用图片

- **接口说明**: 上传材料使用图片
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/upload-material-images`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| images | array | 是 | 图片URL数组，最多上传6张 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "材料使用图片上传成功",
  "data": {
    "images": [
      {
        "id": 1,
        "image_url": "https://example.com/material1.jpg",
        "created_at": "2023-07-25 14:00:00"
      }
    ]
  }
}
```

### 8. 上传完工图片

- **接口说明**: 上传维修完工图片
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/upload-completion-images`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| images | array | 是 | 图片URL数组，最多上传6张 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "完工图片上传成功",
  "data": {
    "images": [
      {
        "id": 1,
        "image_url": "https://example.com/completion1.jpg",
        "created_at": "2023-07-25 14:00:00"
      }
    ]
  }
}
```

### 9. 订单核销

- **接口说明**: 维修完成后师傅核销订单
- **请求方式**: POST
- **请求路径**: `/api/worker/orders/{id}/verify`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| verification_code | string | 是 | 用户提供的核销码 |

- **响应示例**:

```json
{
  "code": 200,
  "message": "核销成功",
  "data": {
    "order_id": 123,
    "order_status": 5,
    "order_status_text": "待维修确认"
  }
}
```

### 10. 获取服务订单大厅列表

- **接口说明**: 获取师傅可接单的订单列表
- **请求方式**: GET
- **请求路径**: `/api/worker/orders/hall`
- **请求参数**:

| 参数名 | 类型 | 是否必填 | 描述 |
|-------|------|---------|------|
| skill_id | integer | 否 | 技能ID筛选 |
| area_id | integer | 否 | 区域ID筛选 |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页记录数，默认10 |

- **响应示例**:

```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "total": 5,
    "list": [
      {
        "id": 123,
        "order_no": "ORD202307251234567890",
        "service_type": "家电维修",
        "service_time": "2023-07-25 09:00-10:00",
        "address": "北京市朝阳区xxx街道xxx小区",
        "amount": "150.00",
        "commission": "80.00",
        "created_at": "2023-07-25 12:34:56",
        "main_image": "https://example.com/image.jpg"
      }
    ]
  }
}
``` 
